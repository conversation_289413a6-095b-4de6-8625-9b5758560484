#!/bin/bash

. /etc/environment

if id -u ubuntu >/dev/null 2>&1; then
    FIVEWAYS_SERVICE_USER='ubuntu'
else
  if [ -z "${FIVEWAYS_SERVICE_USER}" ]; then
    printf "There is no \e[0;31mubuntu\e[0m user on this system.\nPlease set up ubuntu user or specify \e[0;31mFIVEWAYS_SERVICE_USER\e[0m variable (i.e. in /etc/environment file or \e[0;31mexport FIVEWAYS_SERVICE_USER='some_username'\e[0m).\n"
    exit 1
  fi
fi

if [ -z "${FIVEWAYS_SERVICE_MAIN_DIR}" ]; then
  FIVEWAYS_SERVICE_MAIN_DIR='/var/www/'
fi

. "/home/<USER>/.nvm/nvm.sh"

cd "${FIVEWAYS_SERVICE_MAIN_DIR}respondo-ui" || exit

nvm install 14
nvm use 14

/usr/bin/git pull
npm install