@use 'ag-grid-community/styles/ag-grid.css' as ag-grid;
@use 'ag-grid-community/styles/ag-theme-material.css' as ag-theme-material;
@use '../variables.scss' as *;

.ag-theme-alpine {
    overflow-y: auto;

    .ag-root-wrapper {
        border: 1px solid $fiveways-stroke-2;
        border-radius: 8px 8px 0 0;
    }

    .ag-row-selected::before {
        background-color: $fiveways-background-left;
    }

    .ag-header {
        background-color: $fiveways-background-left;
        border: 1px solid $fiveways-stroke-2;
        border-radius: 8px 8px 0 0;
    }

    .ag-header-cell {
        padding: 8px 12px;
        color: #1A9267;
        border-right: 1px solid $fiveways-stroke-2;
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        letter-spacing: .24px;
    }

    .ag-row:hover {
        background-color: $light-grey;
    }

    .ag-row-selected {
        background-color: $fiveways-row-selected;
    }

    .ag-header-cell:hover, .ag-column-hover {
        background-color: inherit;
    }

    .ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within {
        border-color: transparent;
    }

    .ag-cell {
        display: flex;
        align-items: center;
    }

    .ag-icon-asc, .ag-icon-desc {
        color: $fiveways-button-text;
    }

    .ag-row:hover {
        background-color: #F5F5F7 !important;
    }

    .ag-row:focus {
        background-color: #F5F5F7 !important;
    }

    .ag-column-hover {
        background-color: transparent !important;
    }

    .ag-row-selected-custom {
        background-color: #F5F5F7 !important;
    }
}

.ag-theme-alpine:has(.ag-tooltip) {
    padding-bottom: unset;
    height: unset;
}

.corner-icon {
    position: absolute;
    left: 0;
    top: 0;
    width: 33px;
    height: 29px;
    clip-path: polygon(0 0, 100% 0, 0 100%);

    &-mb-24 {
        background: $fiveways-mb24;
    }

    &-assistant {
        background: $fiveways-assistant;
    }

    img {
        height: 15px;
        width: 15px;
        position: relative;
        top: -8px;
        left: 3px;
    }
}

@mixin ellipsis-cell {
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.custom-ellipsis-cell {
    > * {
        max-width: 100%;
    }

    .cell-content {
        @include ellipsis-cell;
        display: block !important;
    }

    span { // musi być span, bo ag-grid przy użyciu "cellRenderer" robi domyślny wrapper span
        max-width: 100%;

        .cell-content {
            @include ellipsis-cell;
        }
    }
}

.custom-padding {
    height: calc(100vh - 235px);
}

html body .ag-theme-alpine .ag-layout-auto-height .ag-center-cols-viewport {
    min-height: unset;
}

.custom-ellipsis-cell:hover {
    text-decoration: underline;
}
