@use "sass:math";
@use "sass:map";
@use '@angular/material' as mat;
@use "variables" as *;
@use "themes/light-theme" as light-theme;
@use 'scss/mixins' as mixins;
@use 'scss/dotline' as dotline;
@use 'scss/buttons' as buttons;
@use 'scss/table' as table;
@use 'scss/quill-override' as quill-override;
@use 'scss/tabs' as tabs;
@use 'scss/badge' as badge;
@use 'scss/accordion' as accordion;
@use 'scss/chips' as chips;
@use 'scss/form-field-styles' as form-field-styles;
@use 'scss/slide-toggle' as slide-toggle;
@use '@covalent/core/theming/all-theme' as all-theme;
@use 'quill/dist/quill.snow.css' as quill;
@use 'ag-grid-community/styles/ag-grid.css' as ag-grid;

@include all-theme.covalent-colors();
@include all-theme.covalent-utilities();
@include mat.core();

$light-theme: mat.define-theme(
    (
        color: (
            theme-type: light,
            primary: light-theme.$primary-palette
        ),
        typography: (
            plain-family: Manrope,
            regular-weight: 400
        ),
        density: (
            scale: -5
        )
    )
);

:root {
    @include mat.all-component-themes($light-theme);

    // Overrides dla chipów
    @include mat.chips-overrides((
        label-text-color: $fiveways-gray,
        elevated-container-color: #EEEEEEFF,
        outline-color: #EEEEEEFF,
        focus-outline-color: red
    ));

    // Overrides dla kart
    @include mat.card-overrides((
        elevated-container-color: $fiveways-white,
        elevated-container-shape: none
    ));

    // Overrides dla pól formularza
    @include mat.form-field-overrides((
        container-text-line-height: 32px,
        container-height: 32px,
        outlined-outline-width: 1px
    ));

    @include mat.autocomplete-overrides((
            background-color: $fiveways-white
    ));

    @include mat.menu-overrides((
            container-color: $fiveways-white
    ));

    @include mat.list-overrides((
        list-item-container-color: transparent,
        list-item-hover-state-layer-opacity: 0,
        list-item-focus-state-layer-opacity: 0,
        list-item-disabled-state-layer-opacity: 0,
        list-item-hover-state-layer-color: transparent,
        list-item-focus-state-layer-color: transparent,
        list-item-disabled-state-layer-color: transparent,
        list-item-one-line-container-height: 56px,
        list-item-two-line-container-height: 72px,
        list-item-three-line-container-height: 88px
    ));

    @include mat.autocomplete-overrides((
        background-color: $fiveways-white
    ));

    @include mat.menu-overrides((
            container-color: $fiveways-white
    ));

    @include tabs.tabs-styles();
    @include badge.badge-styles();
    @include accordion.accordion-styles();

    @include mat.dialog-overrides((
        container-max-width: 100%
    ));

    @include mat.form-field-overrides((
        container-text-line-height: 32px,
        container-height: 32px,
        outlined-outline-width: 1px
    ));

    @include slide-toggle.slide-toggle-styles();
}

quill-editor {
    width: 100% !important;
    height: 100% !important;
    overflow-y: hidden;
}

.mat-mdc-card {
    padding: 16px !important;
}

.mat-drawer {
    --mat-sidenav-container-shape: 0px;
    box-shadow: 5px 0 8px 0 #e6e6e9 !important;
}

.mat-drawer:not(.mat-drawer-side) {
    box-shadow: 0 8px 10px -5px rgba(0, 0, 0, .2), 0 16px 24px 2px rgba(0, 0, 0, .14), 0 6px 30px 5px rgba(0, 0, 0, .12) !important;
}

.mat-mdc-dialog-container {
    --mdc-dialog-container-shape: 8px;
}

.mdc-list-item.mdc-list-item--with-one-line {
    --mdc-list-list-item-one-line-container-height: 52px !important;
}

.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content  {
    padding-top: 3px !important;
}

.mat-mdc-button {
    --mdc-text-button-container-shape: 8px;
    --mdc-text-button-label-text-color: $fiveways-gray;
    background-color: $fiveways-white !important;

    .mdc-button__label {
        color: $fiveways-gray;
        font-family: 'Manrope', sans-serif;
    }
}

.mdc-text-field--outlined:not(.mdc-text-field--disabled) {
    background-color: $fiveways-white;
}

.mat-mdc-form-field {
    min-width: 150px;
    flex: 1;
    --mdc-outlined-text-field-container-shape: 8px;
    --mat-form-field-container-height: 50px;
    --mdc-outlined-text-field-label-text-size: 14px;
    --mdc-outlined-text-field-container-padding-top: 24px;
    --mdc-outlined-text-field-container-padding-bottom: 10px;
    --mat-form-field-container-vertical-padding: 16px;

    // Konfiguracja obramowania - zawsze #DBDFF7
    --mdc-outlined-text-field-outline-color: #DBDFF7;
    --mdc-outlined-text-field-hover-outline-color: #DBDFF7;
    --mdc-outlined-text-field-focus-outline-color: #DBDFF7;
    --mdc-outlined-text-field-disabled-outline-color: rgba(#DBDFF7, 0.7);

    // Konfiguracja tekstu
    --mdc-outlined-text-field-label-text-color: $fiveways-gray;
    --mdc-outlined-text-field-focus-label-text-color: $fiveways-btn-color;
    --mdc-outlined-text-field-hover-label-text-color: $fiveways-gray;
    --mdc-outlined-text-field-input-text-color: $fiveways-gray;
    --mdc-outlined-text-field-input-text-font: 'Manrope', sans-serif;

    .mdc-notched-outline__notch::before {
        border-left: none ;
        border-right: none;
    }

    .mdc-notched-outline__notch::after {
        border-left: none;
        border-right: none;
    }
}

.mat-icon {
    color: $fiveways-button-text;
    fill: $fiveways-button-text;
}

.warn-color {
    color: var(--warn-color);
}

.warn-hint{
    color: var(--warn-color) !important;
}

* {
    box-sizing: border-box;
}

html {
    height: 100%;
}

body {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: 'Manrope', sans-serif;
}

.mat-list-item body {
    font-family: Manrope, sans-serif;
}

.flex {
    flex: 1 1 auto;
}

.cursor-pointer {
    cursor: pointer;
}

.full-width {
    width: 100%;
}

.no-max-width {
    max-width: none;
}

.no-wrap {
    white-space: nowrap;
}

.no-data-font {
    font-size: 30px;
    font-weight: bold;
    letter-spacing: 2px;
    opacity: .2;
}

.panel-info {
    font-size: 30px;
    font-weight: bold;
    height: 100%;
    letter-spacing: 2px;
    margin-top: 0;
    opacity: .2;
}
.info-alert {
    background: $brand-success;
    color: #fff ;
}
.success-snackbar, .success-alert {
    background: $fiveways-button-text;
    color: #fff ;
}

.error-snackbar, .error-alert {
    background: $brand-danger;
    color: #fff ;
}

.warn-snackbar, .warn-alert {
    background: $brand-success;
    color: #fff ;
}

.full-width-dialog {
    max-width: 100vw;
    overflow-y: auto;
}

.full-width {
    width: 100%;
}

.full-height {
    height: 100%;
}

.font-weight-500 {
    font-weight: 500;
}

.font-bold {
    font-weight: bold;
}

.ql-tooltip {
    &::before {
        content: 'Link:' !important;
        color: #000 !important;
    }

    a.ql-remove::before {
        content: '\2715' !important;
        color: #000 !important;
    }

    a.ql-action::after {
        content: '\2699' !important;
        color: #000 !important;
    }

    &[data-mode=link]::before {
        content: 'Link:' !important;
        color: #000 !important;
    }

    &.ql-editing {
        a.ql-action::after {
            content: '\2399' !important;
            font-size: 21px !important;
            color: #000 !important;
        }
    }

    .ql-preview {
        color: #000 !important;
    }
}

.ql-toolbar.ql-snow {
    border-width: 0 0 1px 0 !important;
    border-color: $grey;
}

.ql-container {
    border: none !important;
}

.message-box {
    text-align: center;
}

// scrollbar start

/* Works on Firefox */
* {
    scrollbar-width: thin;
    scrollbar-color: #E2E8ED #F8F8F8;
}

/* Works on Chrome, Edge, and Safari */
*::-webkit-scrollbar {
    width: 15px;
}

*::-webkit-scrollbar-track {
    background: #F8F8F8;
    border-radius: 20px;
}

*::-webkit-scrollbar-thumb {
    background-color: #E2E8ED;
    border-radius: 20px;
    border: 3px solid #F8F8F8;
}

.no-bottom-line {
    border: none;
}

.confirm-dialog {
    button.mat-accent {
        color: $dark-grey;
    }

    h3 {
        border-bottom: 2px solid $fiveways-button-text;
        width: 100%;
        padding-bottom: 10px;
    }
}

// scrollbar stop

.tour-step {
    .mat-card {
        box-shadow: none;
    }

    .mat-card-title {
        .title-text {
            font-size: 16px !important;
        }
    }
}

// full-calendar start

.fc {
    width: 100% !important;
}

.fc-col-header {
    width: 100% !important;
}

.fc-scroller-harness,
.fc-scroller,
.fc-daygrid-body,
.fc-daygrid-body-unbalanced,
.fc-daygrid-body-natural,
.fc-scrollgrid-sync-table{
    width: 100% !important;
    background-color: #FFFFFF !important;
}

.fc-col-header-cell-cushion {
    background-color: #FFFFFF !important;
    color: $fiveways-button-text;
    font-size: 14px !important;
    font-weight: 500 !important;
    margin-top: 7px !important;
    margin-bottom: 7px !important;
    text-transform: capitalize;
}

.fc-daygrid-day,
.fc-daygrid-day-frame {
    min-height: 60px !important;
    color: $fiveways-button-text;
    font-size: 14px !important;
    font-weight: 500 !important;

    @media (min-width: 960px) {
        min-height: 110px !important;
    }
}

.fc-day-other .fc-daygrid-day-number {
    color: #B4B4B4;
}

.fc-day-other {
    background-color: #F8FAFF !important;
}

.fc-daygrid-event {
    margin-top: 5px !important;
    margin-left: 15px !important;
    font-weight: 500 !important;
    color: #000000 !important;
}

.fc-event-title {
    margin-top: 5px !important;
}

.fc-daygrid-day-bottom {
    margin-left: 15px !important;
    font-weight: 400 !important;
}

.fc .fc-daygrid-day.fc-day-today {
    background-color: $hover !important;
}

.fc .fc-event:hover,
.fc .fc-event:focus {
    background-color: inherit !important;
}

// full-calendar stop

.flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.d-none {
    display: none !important;
}

.flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.dropdown-options-container {
    position: fixed;
    margin-top: 4px;
    background: white;
    border: 1px solid $fiveways-stroke;
    border-radius: 4px;
    max-height: 250px;
    overflow-y: auto;
    box-shadow: 5px 0 8px 0 $fiveways-stroke;
}

.dropdown-option {
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
    white-space: nowrap;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dropdown-option:hover {
    background: #f5f5f5;
}

.dropdown-option.selected {
    background: #f0f0f0;
    font-weight: 500;
}

.dropdown-empty-option {
    color: $fiveways-gray;
    border-bottom: 1px solid #eee;
    min-height: 32px;
}

.dropdown-tag-preview {
    width: 8px;
    height: 8px;
    display: inline-block;
    border-radius: 50%;
}
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

app-5ways-button.sync-rotate .icon {
    animation: rotate 1s linear infinite;
}

.link-normalization {
    text-decoration: none;
    color: $fiveways-color-comments;
}
// mat-tabs

.mat-tab-labels {
    border-bottom: 1px solid $fiveways-stroke-2;
}

.mat-tab-group.mat-primary .mat-tab.mat-tab-active {
    color: $fiveways-button-text;
    opacity: 1 !important;
    font-weight: 600;
    border-bottom: 2px solid $fiveways-button-text;

    &:hover {
        background-color: $fiveways-button-secondary-hover-bg;
    }
}

.mat-tab-header, .mat-tab-nav-bar {
    border-bottom: none;
}

.mat-mdc-tab-header {
    margin-bottom: 16px;
}

.mat-tab-header-pagination {
    margin: 0 20px;
}

// mat-accordion

.mat-expansion-panel {
    margin-top: 0 !important;
}

.mat-expansion-panel-body {
    padding: 0 !important;
}

.mat-chip-set {
    margin: 0 !important;
}

.mat-expansion-panel-header .mat-expansion-indicator::after {
    color: $fiveways-button-text;
    fill: $fiveways-button-text;
    stroke: $fiveways-button-text;
}

.mat-badge-content {
    border: 1px solid $fiveways-button-primary-hover-border !important;
    background-color: transparent !important;
    color: $fiveways-button-primary-hover-border !important;
    border-radius: 4px !important;
    min-width: 22px !important;
    min-height: 22px !important;
    font-size: 12px !important;
    margin-left: 5px !important;
    font-weight: bolder !important;
    line-height: 22px !important;
    box-sizing: border-box !important;
    text-align: center !important;
}

.mat-dialog-actions {
    margin-top: $number-16;
}

form .mat-form-field {
    flex: 1;
}

.row .mat-form-field,
.row .mat-mdc-form-field,
.d-flex.row .mat-form-field,
.d-flex.row .mat-mdc-form-field {
    flex: 1;
}

form .mat-form-field:last-of-type {
    margin-bottom: $number-16;
}

body {
    .mat-spinner circle,
    .mat-progress-spinner circle {
        stroke: $fiveways-button-text;
    }

    .mat-progress-bar .mat-progress-bar-fill::after {
        background-color: $fiveways-button-text;
    }

    .mat-progress-bar.mat-progress-bar-indeterminate .mat-progress-bar-primary,
    .mat-progress-bar.mat-progress-bar-indeterminate .mat-progress-bar-secondary {
        background-color: $fiveways-button-text;
    }

    .mat-progress-bar .mat-progress-bar-buffer {
        background-color: rgba($fiveways-button-text, 0.2);
    }
}

:root {
    @include mat.form-field-overrides((
        container-text-size: 13px,
        container-text-line-height: 20px,
    ));
}
