<!doctype html>
<html lang="pl">
<head>
    <link rel="icon" id="appIcon" type="image/x-icon" href="/favicon.ico">
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Geist:wght@100..900&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Lora:ital,wght@0,400..700;1,400..700&family=Manrope:wght@200..800&family=Tinos:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
    <link rel="manifest" href="/manifest.json">
    <link rel="apple-touch-icon" sizes="64x64" href="/assets/images/ios/64.png">
    <link rel="apple-touch-icon" sizes="100x100" href="/assets/images/ios/100.png">
    <link rel="apple-touch-icon" sizes="192x192" href="/assets/images/ios/192.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/images/ios/180.png">
    <link rel="apple-touch-icon" sizes="256x256" href="/assets/images/ios/256.png">
    <link rel="apple-touch-icon" sizes="1024x1024" href="/assets/images/ios/1024.png">
    <meta charset="utf-8">
    <title></title>
    <base href="/">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#015faa">
    <meta name="robots" content="noindex, nofollow">
</head>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/ag-grid/33.0.3/ag-grid-community.min.js" integrity="sha512-2bPPlED0cr8Mg0om7YNIyFkU1Uwsh+FPmDeoCW8QkHKnex+AIGbGXFqI7g7Bz6+TbvLQFpr7PJoZcWPWjzaNqA==" crossorigin="anonymous" referrerpolicy="no-referrer" [defer]="true"></script>
    <script src="https://cdn.jsdelivr.net/npm/lucide@0.482.0/dist/umd/lucide.min.js" [defer]="true"></script>
<body>
<app-root></app-root>
  <noscript>Please enable JavaScript to continue using this application.</noscript>
</body>

<script>
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistrations().then(function (registrations) {
            if (registrations.length) {
                for(let registration of registrations) {
                    registration.unregister();
                }
            }
        });
    }
</script>
</html>
