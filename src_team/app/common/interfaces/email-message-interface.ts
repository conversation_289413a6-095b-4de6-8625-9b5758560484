import {QueueStatus} from '../enums/email-integration.enum';

export interface EmailMessageInterface {
    id?: number | string;
    mail_id?: number;
    mail_server_id?: number | string;
    user_id?: number | string;
    client_id?: string;
    customer_id?: number | string;
    is_read?: number;
    mail_address?: string;
    cc_mail_addresses?: string;
    bcc_mail_addresses?: string;
    mail_to_address: string | string[];
    mail_name?: string;
    sender_mail_address?: string;
    receiver_mail_address?: string;
    mail_subject?: string;
    mail_body?: string;
    mail_to_name: string | string[];
    msq_unique_identifier?: string;
    tags?: string;
    priority?: number;
    total?: number;
    created?: string | Date;
    modified?: string | Date;
    status?: QueueStatus;
    timestamp?: number;
}

export interface MailSend {
    mail_id: number;
    mail_server_id: number;
    mail_to_address: string;
    mail_subject: string;
    mail_to_name: string;
    mail_body: string;
    status: number;
}

export interface MailPriorityResponse {
    status?: string;
    success?: boolean;
    message?: string;
    results?: {
        MailReceivedMysqlMessage?: {
            id: number | string;
            priority: number;
        };
        MailSentMysqlMessage?: {
            id: number | string;
            priority: number;
        };
    }[];
}
