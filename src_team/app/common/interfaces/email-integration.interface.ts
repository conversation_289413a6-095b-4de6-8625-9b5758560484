import { EmailIntegrationStep, ServerEncryption } from '../enums/email-integration.enum';

export interface MailServer {
    id?: number;
    user_name: string;
    user_email: string;
    user_login: string;
    user_password: string;
    reply_to: string;
    fetch_after_date?: string;
    server_protocol: number;
    access_token?: string;
    refresh_token?: string;
    server_address: string;
    server_port: number;
    server_encryption: number;
    priority: number;
    download_attachments?: number;
    mailbox_names?: string;
    server_secure?: number;
    request_read_receipt?: number;
    expiration_timestamp?: number;
    customer_id?: number;
    user_id?: number;
}

export interface MailMailbox {
    dynamic_mailbox_names?: string[];
}

export interface TestMail {
    mail_server_id: number;
    mail_to: string;
}

export interface IMAPData {
    user_name: string;
    user_email: string;
    user_login: string;
    user_password: string;
    server_protocol: number;
    server_address: string;
    server_port: number;
    server_encryption: number;
    download_attachments: number;
    fetch_after_date?: string;
    mailbox_names?: string;
}

export interface MAILOAuth2Data {
    download_attachments: number;
    fetch_after_date?: string;
    mailbox_names?: string;
}

export interface MailFooter {
    id?: number;
    body?: string;
}

export interface MailboxOption {
    value: string;
    label: string;
    active: boolean;
    error: boolean;
}

export interface MailProviders {
    mail_providers: MailProvider[];
}

export interface MailProvider {
    name: string;
    domains: string[];
    smtp?: MailProviderConfig;
    imap?: MailProviderConfig;
}

export interface MailProviderConfig {
    host?: string;
    port?: number;
    encryption?: ServerEncryption;
}

export interface IntegrationChangeStepEvent {
    step: EmailIntegrationStep;
    mailServerSmtpConfig?: MailServer;
    mailServerImapConfig?: MailServer;
}
