import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';
import { TagsComponent } from '../../shared/tags/tags.component';
import { DefaultLayoutDirective } from 'ngx-flexible-layout/flex';
import { DefaultShowHideDirective } from 'ngx-flexible-layout/extended';

@Component({
    selector: 'app-issue-tags-renderer',
    template: `
        <app-tags
            [maxVisibleTags]="1"
            fxLayout fxHide.lt-sm
            objectName="issue"
            place="issue"
            [editMode]="false"
            [tagsIds]="tagsIds"
            [objectId]="objectId">
        </app-tags>
    `,
    imports: [TagsComponent, DefaultLayoutDirective, DefaultShowHideDirective]
})
export class IssueTagsRendererComponent implements ICellRendererAngularComp {
    tagsIds: string = '';
    objectId: number | null = null;

    agInit(params: ICellRendererParams): void {
        if (params.data) {
            this.tagsIds = params.data.tags;
            this.objectId = params.data.id;
        }
    }

    refresh(params: ICellRendererParams): boolean {
        this.agInit(params);

        return true;
    }
}
