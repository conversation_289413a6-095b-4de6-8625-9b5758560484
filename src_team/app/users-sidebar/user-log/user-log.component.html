<div class="toolbar">
    <div class="d-flex">
        <app-5ways-paginator
            class="mr-10"
            [pageSize]="limit"
            [pageIndex]="page-1"
            [length]="(total$ | async) || 0"
            (page)="onPageEvent($event)"
            fxHide.md fxHide.xs fxHide.sm>
        </app-5ways-paginator>

<!-- Ukryte do czasu przerobienia na nasz komponent -->
<!-- <app-date-range-filter (dateRangeChange)="onDateRangeChange($event)"></app-date-range-filter>-->
    </div>
    <div class="d-flex toolbar__filters">
        <app-user-log-type-filter
                (changeType)="onTypeChange($event)"
                [selectedValue]="filters.logType.value">
        </app-user-log-type-filter>
    </div>
</div>

<div class="table-container">
    <div class="table-wrapper" *ngIf="userLogs?.length">
        <ag-grid-angular class="ag-theme-alpine custom-padding"
             [rowData]="userLogs"
             [columnDefs]="columnDefs"
             [defaultColDef]="defaultColDef"
             [rowSelection]="{ mode: 'multiRow' }"
             [getRowStyle]="getRowStyle"
             [rowHeight]="48"
             [headerHeight]="40"
             [gridOptions]="gridOptions"
             (sortChanged)="applySort($event)"
             [context]="{ componentParent: this }"
             [domLayout]="'autoHeight'"
             [suppressRowHoverHighlight]="true">
        </ag-grid-angular>
    </div>

    <div *ngIf="userLogs && userLogs.length === 0" class="no-data">
        {{ 'USER-LOGS.NO-DATA' | translate }}
    </div>
</div>
