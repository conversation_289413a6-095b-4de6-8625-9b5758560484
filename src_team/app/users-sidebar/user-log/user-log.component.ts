import {Component, Input, OnInit} from '@angular/core';
import { HttpErrorResponse, HttpParams } from '@angular/common/http';
import {PageEvent} from '@angular/material/paginator';
import {Observable} from 'rxjs';
import {UserLogService} from '../../services/user-log.service';
import {LogTypes} from '../../common/enums/log-types.enum';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import {ColDef, GridOptions} from 'ag-grid-community';
import {BASE_GRID_OPTIONS} from '../../shared/table/base-grid-options';
import {UserInputStorageService} from '../../services/user-input-storage.service';;
import {getUserLogColumnDefs} from './user-log-columns.config';
import { PaginatorComponent } from '../../elements/paginator/paginator.component';
import { DefaultShowHideDirective } from 'ngx-flexible-layout/extended';
import { UserLogTypeFilterComponent } from '../../shared/user-log-type-filter/user-log-type-filter.component';
import { NgIf, AsyncPipe } from '@angular/common';
import { AgGridModule } from 'ag-grid-angular';

@Component({
    selector: 'app-user-log',
    templateUrl: './user-log.component.html',
    styleUrls: ['./user-log.component.scss'],
    standalone: true,
    imports: [PaginatorComponent, DefaultShowHideDirective, UserLogTypeFilterComponent, NgIf, AgGridModule, AsyncPipe, TranslatePipe]
})
export class UserLogComponent implements OnInit {
    @Input()
        userId: number;

    filters = {
        created: {
            from: '',
            to: '',
            getFilterString: () => {
                const self = this.filters.created;

                if (self.from && self.to) {
                    return 'gt_' + self.from + ',lt_' + self.to;
                }

                if (self.from) {
                    return 'gt_' + self.from;
                }

                if (self.to) {
                    return 'lt_' + self.to;
                }

                return '';
            }
        },
        logType: {
            value: null
        }
    };
    sortField = 'created';
    sortType = 'desc';
    total$: Observable<number>;
    page: number = 1;
    limit: number = 10;
    userLogs = [];
    logTypes: any;
    columnDefs: ColDef[];
    defaultColDef: ColDef = {
        flex: 1,
        sortable: true,
        sortingOrder: ['asc', 'desc'],
        unSortIcon: false,
        filter: true,
        resizable: true,
        cellStyle: {
            padding: '12px 16px',
            fontSize: '14px',
            fontWeight: '400',
            letterSpacing: '0.28px'
        }
    };
    gridOptions: GridOptions = {
        ...BASE_GRID_OPTIONS,
        tooltipShowDelay: 0,
        suppressNoRowsOverlay: true,
        suppressHorizontalScroll: true
    };
    getRowStyle = (params) => {
        return {
            cursor: 'pointer',
            backgroundColor: params.node.selected ? '#F3F7FB' : '#FBFBFB',
            borderBottom: '1px solid #DBDFF7',
        };
    };

    constructor(private userLogService: UserLogService, private translate: TranslateService,  private userInputStorageService: UserInputStorageService,) {
        this.logTypes = LogTypes.reduce((obj, element) => {
            obj[element] = this.translate.instant(`LOG-TYPES-ENUM.${element}`);

            return obj;
        }, {});
    }

    ngOnInit() {
        this.columnDefs = getUserLogColumnDefs(
            this.translate
        );

        this.getPaginationLimit();
        this.getFilters();

        this.total$ = this.userLogService.getTotal();
        this.getUserLogList();
    }

    getUserLogList() {
        this.userLogService.getList(this.requestConditions).subscribe(
            response => {
                this.userLogs = response.map(log => {
                    log.typeDescription = this.logTypes[log.type];

                    return log;
                });
            },
            (error: HttpErrorResponse) => console.error(error)
        );
    }

    applySort(event) {
        setTimeout(() => {
            const sortModel = event.api.getSortModel();

            if (sortModel && sortModel.length > 0) {
                this.sortField = sortModel[0]?.colId;
                this.sortType = sortModel[0]?.sort;
                this.getUserLogList();
            }
        });
    }

    onPageEvent(event: PageEvent): void {
        this.page = event.pageIndex + 1;
        this.limit = event.pageSize;
        this.setPaginationLimit(this.limit);
        this.getUserLogList();
    }

    private getPaginationLimit() {
        this.limit = +this.userInputStorageService.getValue('userLogList_paginationLimit') || 10;
    }

    private getFilters() {
        this.filters.logType.value = +this.userInputStorageService.getValue('userLogList_logType') || null;
        this.filters.created.from = this.userInputStorageService.getValue('userLogList_from') || '';
        this.filters.created.to = this.userInputStorageService.getValue('userLogList_to') || '';
    }

    private setPaginationLimit(limit = 10) {
        this.limit = limit;
        this.userInputStorageService.setValue('userLogList_paginationLimit', limit);
    }

    onTypeChange(typeId: number) {
        this.filters.logType.value = typeId;
        this.userInputStorageService.setValue('userLogList_logType', typeId);

        this.getUserLogList();
    }

    private get requestConditions(): HttpParams {
        let params = new HttpParams();
        params = params.set('?', '');

        if (this.filters.created.getFilterString() !== '') {
            params = params.set('created', this.filters.created.getFilterString());
        }

        if (this.filters.logType.value !== null) {
            params = params.set('type', LogTypes[this.filters.logType.value]);
        }

        params = params
            .set('user_id', this.userId.toString())
            .set('page', this.page.toString())
            .set('limit', this.limit.toString())
            .set('order', `${this.sortField}|${this.sortType}`);

        return params;
    }

    onDateRangeChange(dateRange) {
        this.filters.created.from = dateRange.from;
        this.userInputStorageService.setValue('userLogList_from', this.filters.created.from);
        this.filters.created.to = dateRange.to;
        this.userInputStorageService.setValue('userLogList_to', this.filters.created.to);
        this.page = 1;

        this.getUserLogList();
    }
}
