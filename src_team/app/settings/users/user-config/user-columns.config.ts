import { TranslateService } from '@ngx-translate/core';
import { CellClickedEvent, ColDef, ValueGetterParams } from 'ag-grid-community';
import {isNullOrWhitespace} from '../../../common/utils/string-utils';
import {BlockedInfoRendererComponent} from '../../../components/blocked-info-renderer/blocked-info-renderer.component';
import {UsersListIconsComponent} from '../../../components/user-is-admin/users-list-icons.component';

const baseCellStyle = {
    borderLeft: '1px solid #DBDFF7',
    borderBottom: '1px solid #DBDFF7',
    padding: '12px 16px',
    fontFamily: 'Manrope',
    fontSize: '14px',
    fontWeight: '400',
    letterSpacing: '0.28px'
};

const cellStyles = {
    first: { ...baseCellStyle },
    middle: { ...baseCellStyle },
    last: { ...baseCellStyle, borderRight: '1px solid #DBDFF7' }
};

/**
 * Zwraca definicję kolumn dla użytkowników.
 *
 * Uwagi do użycia klasy CSS 'custom-ellipsis-cell':
 * - stosowane dla komórek, które mogą mieć długi ciąg znaków
 * - wymagane jest nadpisanie metody 'cellRenderer' w celu dodania kontenera z klasą 'cell-content',
 *   ponieważ sama komórka ma ustawiony 'display: flex', co uniemożliwia użycie `text-overflow: ellipsis`.
 */
export function getUsersColumnDefs(
    translate: TranslateService,
    openUserDetails: (event: CellClickedEvent) => void,
): ColDef[] {
    return [
        {
            field: 'lastname',
            headerName: translate.instant('USERS-LIST.LASTNAME-AND-FIRSTNAME'),
            minWidth: 250,
            flex: 2,
            sortable: true,
            sortingOrder: ['asc', 'desc'],
            filter: false,
            cellClass: 'custom-ellipsis-cell',
            valueGetter: (params: ValueGetterParams) => {
                const companyName: string = params.data?.name;
                const firstName: string = params.data?.firstname ?? '';
                const lastName: string = params.data?.lastname ?? '';

                const name: string = (!companyName || companyName === 'undefined') ? `${lastName} ${firstName}` : companyName;

                return isNullOrWhitespace(name) ? '' : name;
            },
            // cellRenderer potrzebny jest dla działania custom-ellipsis-cell (patrz komentarz "getClientsColumnDefs")
            cellRenderer: (params) => {
                return `<div class="cell-content">${params.value}<div>`;
            },
            cellStyle: cellStyles.middle,
            onCellClicked: openUserDetails,
        },
        {
            field: 'email',
            headerName: translate.instant('USERS-LIST.EMAIL'),
            minWidth: 250,
            flex: 2,
            sortingOrder: ['asc', 'desc'],
            sortable: true,
            cellClass: 'custom-ellipsis-cell',
            filter: false,
            icons: {
                sortAscending: '<i-lucide name="chevron-up" style="color: #1A9267; width: 12px; height: 16px; stroke-width: 3px; display: flex; align-items: center;"></i-lucide>',
                sortDescending: '<i-lucide name="chevron-down" style="color: #1A9267; width: 12px; height: 16px; stroke-width: 3px; display: flex; align-items: center;"></i-lucide>',
                sortUnSort: ''
            },
            cellStyle: cellStyles.middle,
            cellRenderer: BlockedInfoRendererComponent,
            onCellClicked: openUserDetails
        },
        {
            field: 'user_role_id',
            headerName: translate.instant('USERS-LIST.ROLE'),
            minWidth: 250,
            flex: 2,
            sortingOrder: ['asc', 'desc'],
            sortable: true,
            cellClass: 'custom-ellipsis-cell',
            filter: false,
            valueGetter: (params: ValueGetterParams) => {
                return params.data.role.name;
            },
            cellRenderer: UsersListIconsComponent,
            cellStyle: cellStyles.middle,
            onCellClicked: openUserDetails
        }
    ];
}
