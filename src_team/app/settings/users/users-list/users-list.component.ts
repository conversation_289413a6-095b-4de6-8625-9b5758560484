import {Component, forwardRef, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {MatDialog} from '@angular/material/dialog';
import {BreakpointObserver, Breakpoints} from '@angular/cdk/layout';
import {map} from 'rxjs/operators';
import {Observable, Subscription, timer} from 'rxjs';
import {UserAddComponent} from '../user-add/user-add.component';
import {UserService} from '../../../services/user.service';
import {UserInputStorageService} from '../../../services/user-input-storage.service';
import {UserInterface} from '../../../common/interfaces/user.interface';
import {AuthService} from '../../../services/auth/auth.service';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import {CustomerService} from '../../../services/Customer.service';
import {DialogDetailsComponent} from '../../../shared/dialog-details/dialog-details.component';
import {DefaultClassDirective, DefaultShowHideDirective} from 'ngx-flexible-layout/extended';
import {ButtonVariant} from '../../../common/enums/button-variant.enum';
import {ButtonComponent} from '../../../elements/button/button.component';
import {LucideAngularModule} from 'lucide-angular';
import {AgGridAngular} from 'ag-grid-angular';
import {ColDef, ColumnState, GridOptions, SortChangedEvent} from 'ag-grid-community';
import {BASE_GRID_OPTIONS} from '../../../shared/table/base-grid-options';
import {NgIf} from '@angular/common';
import {PageEvent} from '@angular/material/paginator';
import {getUsersColumnDefs} from '../user-config/user-columns.config';
import {PaginatorComponent} from '../../../elements/paginator/paginator.component';
import {SearchInputComponent} from '../../../elements/search-input/search-input.component';
import {FormsModule} from '@angular/forms';
import {UserGroupFilterComponent} from '../../../shared/user-group-filter/user-group-filter.component';
import {UserRoleFilterComponent} from '../../../shared/user-role-filter/user-role-filter.component';
import {UserStatusFilterComponent} from '../../../shared/user-status-filter/user-status-filter.component';
import {HttpParams} from '@angular/common/http';

@Component({
    selector: 'app-users-list',
    templateUrl: './users-list.component.html',
    styleUrls: ['./users-list.component.scss'],
    imports: [forwardRef(() => DialogDetailsComponent), DefaultClassDirective, TranslatePipe, ButtonComponent, LucideAngularModule, AgGridAngular, NgIf, PaginatorComponent, DefaultShowHideDirective, SearchInputComponent, FormsModule, UserGroupFilterComponent, UserRoleFilterComponent, UserStatusFilterComponent]
})

export class UsersListComponent implements OnInit, OnDestroy {
    displayedColumns = ['name', 'email', 'role'];
    users: UserInterface[];
    userLimit = 0;
    disabledAdd = false;
    columnDefs: ColDef[];
    defaultColDef: ColDef = {
        flex: 1,
        sortable: true,
        sortingOrder: ['asc', 'desc'],
        unSortIcon: false,
        filter: true,
        resizable: true,
        cellStyle: {
            padding: '12px 16px',
            fontSize: '14px',
            fontWeight: '400',
            letterSpacing: '0.28px'
        }
    };
    gridOptions: GridOptions = {
        ...BASE_GRID_OPTIONS,
        tooltipShowDelay: 0,
        suppressNoRowsOverlay: true,
        suppressHorizontalScroll: true,
        domLayout: 'autoHeight',
        rowHeight: 48
    };
    getRowStyle = (params) => {
        return {
            cursor: 'pointer',
            backgroundColor: params.node.selected ? '#F3F7FB' : '#FBFBFB',
            borderBottom: '1px solid #DBDFF7',
        };
    };
    limit: number;
    page: number;
    totalUsers: number;
    sortField = 'is_admin';
    sortType = 'desc';
    filters: object;
    userEditedSubscription: Subscription;
    isHandset$: Observable<boolean>;

    // wartości filtrów
    groupFilterVal;
    roleFilterVal;
    statusFilterVal;
    userNameFilter;
    isAdmin;
    isDetailsOpen = false;
    detailsData: UserInterface;
    detailsModel = 'users';

    private _userService: UserService = inject(UserService)
    private dialog: MatDialog = inject(MatDialog);
    private breakpointObserver: BreakpointObserver = inject(BreakpointObserver);
    private userInputStorageService: UserInputStorageService = inject(UserInputStorageService);
    private _authService: AuthService = inject(AuthService);
    private customerService: CustomerService = inject(CustomerService);
    private translate: TranslateService = inject(TranslateService);
    private refreshSub: Subscription;

    protected readonly ButtonVariant = ButtonVariant;

    constructor() {
        this.isHandset$ = this.breakpointObserver.observe(Breakpoints.Handset).pipe(map(result => result.matches));

        this.refreshSub = this._userService.refreshUsersTable$.subscribe(() => {
            this.getUsersData();
        });
    }

    private get requestConditions(): HttpParams {
        let params = new HttpParams();

        if (this.userNameFilter) {
            params = params.set('search', this.userNameFilter);
        }

        if (this.groupFilterVal > 0) {
            params = params.set('UserGroup_group_id', this.groupFilterVal);
        }

        if (this.roleFilterVal !== undefined) {
            params = params.set('user_role_id', this.roleFilterVal);
        }

        if (this.statusFilterVal !== undefined) {
            params = params.set('banned', this.statusFilterVal);
        }

        params = params
            .set('search_in', 'firstname,lastname,email')
            .set('is_client', '0')
            .set('page', this.page.toString())
            .set('limit', this.limit.toString())
            .set('order', `${this.sortField}|${this.sortType}`);

        return params;
    }

    private getUsersData() {
        this._userService.getActiveUserCounter().subscribe(data => {
            this.disabledAdd = +data.userCounter >= +this.userLimit;
        });

        this._userService.getUsers(this.requestConditions, this.groupFilterVal).subscribe(data => {
            this.totalUsers = +data.total;
            this.users = data.results;
        });
    }

    private loadFilterSettings() {
        const filterSettings = JSON.parse(this.userInputStorageService.getValue('settings_users_filters'));

        if (filterSettings) {
            this.groupFilterVal = filterSettings.groupId;
            this.roleFilterVal = filterSettings.roleId;
            this.statusFilterVal = filterSettings.banned;
            this.sortField = filterSettings.sortField;
            this.sortType = filterSettings.sortType;
        }
    }

    private saveFilterSettings() {
        this.filters = {
            groupId: this.groupFilterVal,
            roleId: this.roleFilterVal,
            banned: this.statusFilterVal,
            userName: this.userNameFilter,
            sortField: this.sortField,
            sortType: this.sortType
        };

        this.userInputStorageService.setValue('settings_users_filters', JSON.stringify(this.filters));
    }

    ngOnInit() {
        this.columnDefs = getUsersColumnDefs(
            this.translate,
            this.openUserDetails.bind(this),
        );

        this.page = 1;
        this.sortField = 'lastname';
        this.sortType = 'desc';

        this.loadFilterSettings();

        this.getPaginationLimit();

        this.customerService.getList()
            .subscribe(data => {
                this.userLimit = +data[0]['user_limit'];
                this.getUsersData();

                this._userService.getUser(this._authService.getUserId())
                    .subscribe(userData => {
                        this.isAdmin = +userData.is_admin;
                    });
            });
    }

    openUserDetails(userDetails: UserInterface) {
        const delayTime = 300;

        if (+this.detailsData?.id !== +userDetails?.id) {
            this.isDetailsOpen = false,
                timer(delayTime).subscribe(() => {
                    this.detailsData = userDetails;
                    this.detailsModel = 'users';
                    this.isDetailsOpen = true;
                })
        } else {
            this.isDetailsOpen = !this.isDetailsOpen
        }
    }

    editUserData(userId: number) {
        this.dialog.open(UserAddComponent, {
            width: '690px',
            data: {
                userId: userId
            },
            autoFocus: true,
            panelClass: 'full-width-dialog'
        });
    }

    onGroupChange(value) {
        this.groupFilterVal = value === undefined ? value : +value;
        this.saveFilterSettings();
        this.getUsersData();
    }

    onRoleChange(value) {
        this.roleFilterVal = value;
        this.saveFilterSettings();
        this.getUsersData();
    }

    onStatusChange(value) {
        this.statusFilterVal = value;
        this.saveFilterSettings();
        this.getUsersData();
    }

    ngOnDestroy() {
        this.saveFilterSettings();

        if (this.userEditedSubscription) {
            this.userEditedSubscription.unsubscribe();
        }

        this.refreshSub?.unsubscribe();
    }

    searchByName() {
        this.getUsersData();
    }

    clearSearchByName() {
        this.userNameFilter = '';
        this.getUsersData();
    }

    public applySort(event: SortChangedEvent<UserInterface>) {
        if (!event.api) {
            console.error('Grid API not available in sortChanged event');
            return;
        }

        const columnStates: ColumnState[] = event.api.getColumnState();

        const sortedColumns = columnStates.filter(state => state.sort != null);

        if (sortedColumns.length > 0) {
            sortedColumns.sort((a, b) => (a.sortIndex ?? Infinity) - (b.sortIndex ?? Infinity));

            const primarySortState = sortedColumns[0];

            this.sortField = primarySortState.colId;
            this.sortType = primarySortState.sort as 'asc' | 'desc';
        } else {
            this.sortField = 'is_admin';
            this.sortType = 'desc';
        }

        this.page = 1;
        this.saveFilterSettings();
        this.getUsersData();
    }

    private getPaginationLimit() {
        const paginationLimit = this.userInputStorageService.getValue('userList_paginationLimit');
        this.limit = paginationLimit ? +paginationLimit : 10;
    }

    private setPaginationLimit(limit = 10) {
        this.limit = limit;
        this.userInputStorageService.setValue('userList_paginationLimit', limit);
    }

    onPageEvent(event: PageEvent): void {
        this.page = event.pageIndex + 1;
        this.limit = event.pageSize;
        this.setPaginationLimit(this.limit);
        this.getUsersData();
    }

    removeFilters() {
        Promise.resolve()
            .then(() => {
                this.userInputStorageService.removeItem('settings_users_filters');

                return { success: true, message: 'Filters cleared successfully' };
            })
            .then((result) => {
                if (result.success) {
                    this.groupFilterVal = undefined;
                    this.roleFilterVal = undefined;
                    this.statusFilterVal = undefined;
                    this.sortField = 'lastname';
                    this.sortType = 'asc';

                    this.getUsersData();

                    return { success: true, message: 'Filters reset and UI updated' };
                }
            })
            .catch(error => {
                console.error('Error clearing filters:', error);
            });
    }
}
