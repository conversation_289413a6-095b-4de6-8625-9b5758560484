@use '../../../../variables' as *;

.actions-section {
    padding: 10px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-right: 43px;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    flex-wrap: wrap;
    margin-bottom: 14px;

    &__filters {
        gap: 10px;
    }
}

.table-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.table-wrapper {
    flex-grow: 1;
    margin: 0 10px 0 10px;
}

.no-data {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
    font-size: 30px;
    font-weight: bold;
    letter-spacing: 2px;
    margin-top: 0;
    opacity: 0.2;
}

.message-box {
    align-items: center;
    display: flex;
    justify-content: center;
    background-color: white;
    padding: 16px;
    margin: 16px;

    .message-text {
        margin-left: 8px;
    }

    .user-disabled {
        color: $orange;
    }
}