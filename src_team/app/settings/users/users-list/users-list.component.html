<app-dialog-details
        [isOpen]="isDetailsOpen"
        [data]="detailsData"
        [model]="detailsModel"
        (isOpenChange)="isDetailsOpen = $event">
</app-dialog-details>

<div class="actions-section">
    <app-5ways-button [variant]="ButtonVariant.PRIMARY" iconLeft="plus" (click)="editUserData(0)" ngClass.xs="is-mobile">
        {{ 'USERS-LIST.ADD' | translate }}
    </app-5ways-button>
</div>
<div class="toolbar">
    <div class="d-flex">
        <app-5ways-paginator
                class="mr-10"
                [pageSize]="limit"
                [pageIndex]="page-1"
                [length]="totalUsers"
                (page)="onPageEvent($event)"
                fxHide.md fxHide.xs fxHide.sm>
        </app-5ways-paginator>
        <app-5ways-search-input
                [(ngModel)]="userNameFilter"
                (searchTriggered)="searchByName()"
                placeholder="{{ 'CLIENTS-LIST.SEARCH' | translate }}">
        </app-5ways-search-input>
    </div>
    <div class="d-flex toolbar__filters">
        <app-user-group-filter class="toolbar-btn" [selectPlaceholder]="'USER-FILTERS.GROUP' | translate" (changeGroup)="onGroupChange($event)" [selectedValue]="groupFilterVal" ngClass.xs="full-width"></app-user-group-filter>
        <app-user-role-filter class="toolbar-btn" [selectPlaceholder]="'USER-FILTERS.ROLE' | translate" (changeRole)="onRoleChange($event)" [selectedValue]="roleFilterVal" ngClass.xs="full-width"></app-user-role-filter>
        <app-user-status-filter class="toolbar-btn" [selectPlaceholder]="'USER-FILTERS.STATUS' | translate" (changeStatus)="onStatusChange($event)" [selectedValue]="statusFilterVal" ngClass.xs="full-width"></app-user-status-filter>
        <app-5ways-button iconLeft="trash-2" [variant]="ButtonVariant.GHOST" class="mr-4" (click)="removeFilters()"></app-5ways-button>
    </div>
</div>

@if (disabledAdd) {
    <div class="message-box">
        <i-lucide class="user-disabled" name="user-lock"></i-lucide>
        <span class="message-text">{{ 'USERS-LIST.USER-LIMIT-MESSAGE' | translate }}
            <!--            <span class="message-text">{{ 'USERS-LIST.USER-LIMIT-MESSAGE' | translate }} <span *ngIf="isAdmin" class="link" (click)="openProfile()">{{ 'USERS-LIST.USER-LIMIT-LINK' | translate }}</span>-->
            </span>
    </div>
}

<div class="table-container">
    <div class="table-wrapper" *ngIf="users?.length">
        <ag-grid-angular class="ag-theme-alpine custom-padding"
             [rowData]="users"
             [columnDefs]="columnDefs"
             [defaultColDef]="defaultColDef"
             [getRowStyle]="getRowStyle"
             [rowHeight]="48"
             [headerHeight]="40"
             [gridOptions]="gridOptions"
             (sortChanged)="applySort($event)"
             [context]="{ componentParent: this }"
             [suppressRowHoverHighlight]="true">
        </ag-grid-angular>
    </div>

    <div *ngIf="users && users.length === 0" class="no-data">
        {{ 'USERS-LIST.NO-RESULTS' | translate}}
    </div>
</div>