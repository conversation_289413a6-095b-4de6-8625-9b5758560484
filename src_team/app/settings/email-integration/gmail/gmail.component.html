<div *ngIf="mailServerId && refresh_token" class="sync-status">
    <app-5ways-icon name="circle-check" stroke="green"></app-5ways-icon>
    <div class="email-address">
        <strong>Email: </strong> <span>{{ emailValue }}</span>
    </div>
</div>


<form *ngIf="mailServerId && refresh_token" [formGroup]="userIntegrationData" (ngSubmit)="onSubmit()" class="mt-20" autocomplete="off">
    <div class="d-flex row" [style.display]="'none'">
        <mat-form-field appearance="outline" class="w-30 mr-20">
            <mat-label>{{ 'MAIL-INTEGRATION.ATTACHMENTS.LABEL' | translate }}</mat-label>
            <mat-select formControlName="downloadAttachments" [required]="true">
                <mat-option
                        [value]="DownloadAttachments.OFF">{{ 'MAIL-INTEGRATION.ATTACHMENTS.OPTIONS.NO' | translate }}
                </mat-option>
                <mat-option
                        [value]="DownloadAttachments.ON">{{ 'MAIL-INTEGRATION.ATTACHMENTS.OPTIONS.YES' | translate }}
                </mat-option>
            </mat-select>
            <mat-error *ngIf="userIntegrationData.get('downloadAttachments').hasError('required')">
                {{ 'MAIL-INTEGRATION.ATTACHMENTS.ERROR.REQUIRED' | translate }}
            </mat-error>
        </mat-form-field>
    </div>

    <div class="d-flex row" [style.display]="'none'">
        <mat-form-field appearance="outline" class="date-field w-30 mr-20">
            <mat-label>{{ 'MAIL-INTEGRATION.FETCH-MESSAGES.LABEL' | translate }}</mat-label>
            <input matInput
                   [matDatepicker]="picker"
                   formControlName="fetchAfterDate"
                   [min]="minDate"
                   [max]="today"
                   [required]="true">
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>
    </div>

    <div class="w-61 d-flex justify-between action-btns">
        <app-5ways-button [variant]="ButtonVariant.SECONDARY" type="button" (click)="onDelete()"
                [disabled]="!mailServerId">
            {{ 'MAIL-INTEGRATION.BUTTONS.DISABLE-INTEGRATION' | translate }}
        </app-5ways-button>
        <app-5ways-button [variant]="ButtonVariant.PRIMARY" type="submit" [disabled]="!userIntegrationData.valid || userIntegrationData.pristine">
            {{ 'MAIL-INTEGRATION.BUTTONS.SAVE' | translate }}
        </app-5ways-button>
    </div>
</form>
