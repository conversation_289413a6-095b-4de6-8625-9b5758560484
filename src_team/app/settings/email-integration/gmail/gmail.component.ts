import { NgIf } from '@angular/common';
import { Component, EventEmitter, inject, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatOption } from '@angular/material/autocomplete';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, MatDatepickerInput, MatDatepickerToggle } from '@angular/material/datepicker';
import { MatDialog } from '@angular/material/dialog';
import { MatInput } from '@angular/material/input';
import { MatError, MatFormField, MatLabel, MatSelect, MatSuffix } from '@angular/material/select';
import { ActivatedRoute, Params, Router } from '@angular/router';

import { Store } from '@ngrx/store';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

import { ButtonVariant } from '../../../common/enums/button-variant.enum';
import { ButtonComponent } from '../../../elements/button/button.component';
import { AlertType } from 'src_team/app/common/enums/alert-type.enum';
import { AlertService } from 'src_team/app/services/alert.service';
import { DownloadAttachments, EmailIntegrationStep, ServerProtocol } from '../../../common/enums/email-integration.enum';
import { IntegrationChangeStepEvent, MAILOAuth2Data, MailServer } from '../../../common/interfaces/email-integration.interface';
import { MailGmailAuthCodeData } from '../../../common/interfaces/mail-o-auth2.interface';
import { EmailIntegrationService } from '../../../services/email-integration.service';
import { EmailService } from '../../../services/email.service';
import { ConfirmDialogComponent } from '../../../shared/confirm-dialog/confirm-dialog.component';
import { setMailServerState } from '../../../store/mail-server/mail-server.actions';
import { formatDateToYMD, getMinDate } from '../helpers/dateTimeHelper';
import { IconComponent } from 'src_team/app/elements/icon/icon.component';

@Component({
    selector: 'app-gmail',
    templateUrl: './gmail.component.html',
    styleUrls: ['./gmail.component.scss'],
    imports: [
      NgIf,
      FormsModule,
      ReactiveFormsModule,
      MatFormField,
      MatLabel,
      MatSelect,
      MatOption,
      MatError,
      MatInput,
      MatDatepickerInput,
      MatDatepickerToggle,
      MatSuffix,
      MatDatepicker,
      TranslatePipe,
      ButtonComponent,
      IconComponent,
    ]
})
export class GmailComponent implements OnInit, OnDestroy {

  @Output() changeIntegrationStep: EventEmitter<IntegrationChangeStepEvent> = new EventEmitter<IntegrationChangeStepEvent>();

  @Input() mailServerConfiguration: MailServer | undefined;

  public oAuthRedirectUrl: string = '';
  public DownloadAttachments: typeof DownloadAttachments = DownloadAttachments;
  public userIntegrationData: FormGroup;
  public mailServerId: number | undefined; // id wpisu w mail_server
  public refresh_token: string | undefined = '';
  public integrationDataSub$: Subscription;
  public today: Date = new Date();
  public minDate: Date = getMinDate();

  protected readonly ButtonVariant: typeof ButtonVariant = ButtonVariant;

  private queryParamsSubscription: Subscription;
  private readonly alertService: AlertService = inject(AlertService);

  constructor(
    private readonly emailIntegrationService: EmailIntegrationService,
    private readonly translateService: TranslateService,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly emailService: EmailService,
    private readonly dialog: MatDialog,
    private readonly translate: TranslateService,
    private readonly store: Store
  ) { }

  ngOnInit(): void {
    this.initGmailForm();

    if (this.mailServerConfiguration) {
      this.setIntegrationData(this.mailServerConfiguration);
    }

    this.queryParamsSubscription = this.route.queryParams.subscribe((params: Params): void => {
      if (params['code'] && params['scope']) {
        this.setAuthCredentialsAndGetEmail(params['code'], params['scope']);
      } else {
        if (!this.mailServerId) {
          this.loadGmailAuthUrl();
        }
      }
    });
  }

  ngOnDestroy(): void {
    if (this.queryParamsSubscription) {
      this.queryParamsSubscription?.unsubscribe();
    }
  }

  public get emailValue(): unknown {
    return this.userIntegrationData.get('email')?.value;
  }

  public initGmailForm(): void {
    this.userIntegrationData = new FormGroup({
      email: new FormControl('', [Validators.email, Validators.required]),
      downloadAttachments: new FormControl(DownloadAttachments.ON, [Validators.required, Validators.pattern('^[0-1]$')]),
      fetchAfterDate: new FormControl(this.today, Validators.required)
    });
  }

  public fetchIntegrationData(): void {
    this.integrationDataSub$ = this.emailIntegrationService.getIntegrationSettingsData(ServerProtocol.GMAIL).subscribe(async (result: MailServer[]): Promise<void> => {
      if (result && result.length > 0) {
        const mailServerData: MailServer = result[0];
        this.setIntegrationData(mailServerData);

        const currentParams: Params = this.route.snapshot.queryParams;
        if (currentParams?.code || currentParams?.scope) {
          await this.router.navigate([`/settings/email-integration/gmail`]);
        }
      } else {
        if (!this.mailServerId || !this.refresh_token) {
          this.loadGmailAuthUrl();
        }
      }
    });
  }

  public patchFormValues(data: MailServer): void {
    const fetchAfterDate: Date = data.fetch_after_date ? new Date(data.fetch_after_date) : null;
    this.userIntegrationData.patchValue({
      email: data.user_email,
      downloadAttachments: +data.download_attachments,
      fetchAfterDate: fetchAfterDate
    });
  }

  public onSubmit(): void {
    if (this.userIntegrationData.valid) {
      let fetchAfterDateValue: unknown = this.userIntegrationData.get('fetchAfterDate').value;
      if (fetchAfterDateValue) {
        fetchAfterDateValue = new Date(fetchAfterDateValue as string);
      }

      const formData: MAILOAuth2Data = {
        download_attachments: this.userIntegrationData.get('downloadAttachments').value,
        fetch_after_date: formatDateToYMD(fetchAfterDateValue)
      };

      if (this.mailServerId) {
        this.emailIntegrationService.updateIntegrationData(this.mailServerId, formData).subscribe(() => {
          this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.UPDATED'), AlertType.SUCCESS);
        }, (error: unknown): void => {
          console.error(error);
          this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.UPDATE-ERROR'), AlertType.ERROR);
        });
      } else {
        this.emailIntegrationService.postIntegrationData(formData).subscribe(() => {
          this.fetchIntegrationData();
          this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.INTEGRATION-SUCCESS'), AlertType.SUCCESS);
        }, (error: unknown): void => {
          console.error(error);
          this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.INTEGRATION-ERROR'), AlertType.ERROR);
        });
      }
    } else {
      this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.FORM-INVALID'), AlertType.ERROR);
    }
  }

  public loadGmailAuthUrl(): void {
    if (!this.mailServerId) {
      this.emailService.getGmailAuthUrl().subscribe((redirectUrl: string): void => {
        this.oAuthRedirectUrl = redirectUrl;
        this.onOAuthConsent();
      });
    }
  }

  public setAuthCredentialsAndGetEmail(code: string, scope: string): void {
    const data: MailGmailAuthCodeData = {
      MailGmailAuth: {
        code,
        scope,
      }
    };

    if (this.mailServerId) {
      this.emailService.updateGmailAuthCredentialsAndGetEmail(this.mailServerId, data).subscribe({
        next: () => {
          this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.INTEGRATION-REFRESH-SUCCESS'), AlertType.SUCCESS);
          this.fetchIntegrationData();
        },
        error: (err: unknown): void => {
          console.error(err);
          this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.INTEGRATION-REFRESH-ERROR'), AlertType.ERROR);
        }
      });
    } else {
      this.emailService.setGmailAuthCredentialsAndGetEmail(data).subscribe({
        next: () => {
          this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.INTEGRATION-SUCCESS'), AlertType.SUCCESS);
          this.fetchIntegrationData();
        },
        error: (err: unknown): void => {
          console.error(err);
          this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.INTEGRATION-ERROR'), AlertType.ERROR);
        }
      });
    }

  }

  public onOAuthConsent(): void {
    if (this.oAuthRedirectUrl) {
      window.location.href = this.oAuthRedirectUrl;
    }
  }

  public onDelete(): void {
    this.dialog.open(
        ConfirmDialogComponent,
        {
          width: '400px',
          data: {
            header: this.translate.instant('MAIL-INTEGRATION.TITLE'),
            description: this.translate.instant('MAIL-INTEGRATION.MODAL-CHOOSE-DISCONNECT.DESCRIPTION'),
          },
          panelClass: 'full-width-dialog',
          disableClose: true
        }
    ).afterClosed().subscribe((isConfirmed: boolean): void => {
      if (isConfirmed) {
        if (this.mailServerId) {
          this.emailIntegrationService.deleteIntegrationData(this.mailServerId).subscribe(() => {
            this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.GMAIL-DISABLED'), AlertType.SUCCESS);
            this.userIntegrationData.reset(
                {
                  name: '',
                  email: '',
                  downloadAttachments: DownloadAttachments.OFF,
                  fetchAfterDate: this.today
                }
            );
            this.mailServerConfiguration = undefined;
            this.mailServerId = null;
            this.store.dispatch(setMailServerState({
              id: null,
              is_mail_server_token_related: false,
              is_refresh_token_active: null,
              server_protocol_name: null
            }));
            this.changeIntegrationStep.emit({
              step: EmailIntegrationStep.SELECT_INTEGRATION_METHOD
            });
          }, (error: unknown): void => {
            console.error(this.translateService.instant('MAIL-INTEGRATION.GMAIL-ERROR', error));
            this.alertService.showAlert(this.translateService.instant('MAIL-INTEGRATION.GMAIL-DISABLE-ERROR'), AlertType.ERROR);
          });
        }
      }
    });
  }

  private setIntegrationData(mailServerConfiguration: MailServer): void {
    this.mailServerId = mailServerConfiguration.id;
    this.patchFormValues(mailServerConfiguration);
    this.refresh_token = mailServerConfiguration.refresh_token;
  }
}
