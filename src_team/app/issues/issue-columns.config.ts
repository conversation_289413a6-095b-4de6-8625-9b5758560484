import {CellClickedEvent, ColDef, ValueGetterParams} from 'ag-grid-community';
import {DurationPipe} from '../shared/pipes/duration.pipe';
import {ClientInfo, MainInfoValue, StatusStyles} from '../interfaces/issue-columns';
import {TranslateService} from '@ngx-translate/core';
import {IssueMoreOptionsRendererComponent} from '../components/more-options-renderer/issue-more-options-renderer.component';
import {environment} from '../../environments/environment';
import {MiddleClickCellRendererComponent} from '../components/middle-click-cell-renderer/middle-click-cell-renderer.component';
import {UserCellRendererComponent} from '../components/user-cell-renderer/user-cell-renderer.component';
import {IssueCategoriesService} from '../services/issue-categories.service';
import {IssueTagsRendererComponent} from '../components/mail-tags-renderer/issue-tags-renderer.component';
import {IssueColumn} from '../common/enums/issue-column.enum';
import {DatePipe} from '@angular/common';
import {IssueNameRendererComponent} from '../components/issue-name-renderer/issue-name-renderer.component';

/**
 * Funkcja pomocnicza do normalizacji tekstu – usuwa znaki inne niż litery i zamienia na małe litery.
 */
function normalizeString(str: string | null | undefined): string {
    if (!str) {
        return '';
    }

    return String(str).replace(/[^a-zA-Z]/g, '').toLowerCase();
}

/**
 * Funkcja pomocnicza wyszukująca klucz w obiekcie po normalizacji.
 */
function findNormalizedKey(obj: any, targetKey: string): string | undefined {
    if (!obj || typeof obj !== 'object') {
        return undefined;
    }

    return Object.keys(obj).find(key => normalizeString(key) === normalizeString(targetKey));
}

function createMb24Badge(): string {
    return `
    <div class="corner-icon corner-icon-mb-24">
       <span class="triangle triangle-mb24" title="Biuro rachunkowe">
        <img class="triangle" src="../../assets/images/mb24-white.svg" alt="MB24" />
      </span>
    </div>`;
}

function createNoAssistantBadge(): string {
    return `
    <div class="corner-icon corner-icon-assistant">
      <span class="triangle-assistant" title="Brak asystenta">
        <img src="../../assets/images/no-assistant.svg" alt="Brak asystenta" />
      </span>
    </div>`;
}

function checkAssistantStatus(mainInfo: MainInfoValue): boolean {
    if (!mainInfo) {
        return false;
    }

    const activePackages = mainInfo[findNormalizedKey(mainInfo, 'Aktywne pakiety')] ||
        mainInfo[findNormalizedKey(mainInfo, 'Aktywny pakiet')] || '';
    const extras = mainInfo[findNormalizedKey(mainInfo, 'Dodatki')] || '';

    return normalizeString(activePackages).includes(normalizeString('asystentem')) ||
        normalizeString(extras).includes(normalizeString('asystentem'));
}

function checkMb24Status(mainInfo: MainInfoValue | null, decodedInfo: ClientInfo[]): boolean {
    if (!mainInfo || !decodedInfo?.length) {
        return false;
    }

    if (typeof decodedInfo[0]?.value === 'object') {
        const mb24Value = mainInfo[findNormalizedKey(mainInfo, 'Biuro rachunkowe')] || '';
        const mb24CompanyValue = mainInfo[findNormalizedKey(mainInfo, 'Klient biura')] || '';

        return [mb24Value, mb24CompanyValue]
            .map(val => normalizeString(val))
            .some(value => value === normalizeString('tak'));
    }

    return decodedInfo.some(elem =>
        elem?.key && elem?.value &&
        normalizeString(elem.key) === normalizeString('Biuro rachunkowe') &&
        normalizeString(elem.value) === normalizeString('Tak')
    );
}

function getStatusStyles(status: string): StatusStyles {
    const statusMap: Record<string, { color: string; text: string }> = {
        new: { color: '#14EB8C', text: 'Nowa' },
        open: { color: '#3392FF', text: 'Otwarta' },
    };

    return {
        backgroundColor: statusMap[status]?.color || '#6c757d',
        textColor: '#000',
        textValue: statusMap[status]?.text
    };
}

function createStatusBadge(status: string | null | undefined): string {
    const normalizedStatus = String(status || '').toLowerCase();
    const { backgroundColor, textColor, textValue } = getStatusStyles(normalizedStatus);

    const badgeStyle = `
        display: inline-block;
        color: ${textColor};
        width: 12px;
        height: 12px;
        background-color: ${backgroundColor};
        border-radius: 50%;
        border: 1px solid #EAF2FF;
        display: flex;
        justify-content: center;
    `;

    return `<span style="${badgeStyle}"></span>`;
}

/**
 * Funkcja renderująca komórkę kolumny status.
 * Dekoduje dane z pola client_info (jeśli istnieją) i w zależności od uzyskanych wartości
 * wstawia ikonę (np. dla MB24) przed badge statusu.
 */
function statusBadgeCellRenderer(params): string {
    if (!params?.data?.client_info) {
        return createStatusBadge(params?.value);
    }

    try {
        const clientInfo = JSON.parse(atob(params.data.client_info));
        if (!clientInfo) {
            return createStatusBadge(params?.value);
        }

        const decodedInfo = Object.entries(clientInfo)
            .map(([key, value]) => ({ key, value }))
            .filter(item => item.key != null);

        const mainInfoItem = decodedInfo.find(item =>
            normalizeString(item.key) === normalizeString('Dane podstawowe'));

        const mainInfo = mainInfoItem?.value as MainInfoValue;

        if (!mainInfo) {
            return createStatusBadge(params?.value);
        }

        const isAssistant = checkAssistantStatus(mainInfo);
        const isMb24Issue = checkMb24Status(mainInfo, decodedInfo);

        const iconHtml = isMb24Issue ? createMb24Badge() :
            (!isAssistant && !isMb24Issue) ? createNoAssistantBadge() : '';

        return iconHtml + createStatusBadge(params?.value);
    } catch (e) {
        console.error('Błąd dekodowania client_info:', e);
        return createStatusBadge(params?.value);
    }
}

function triangleBadgeCellRenderer(params): string {
    if (!params?.data?.client_info || !environment.internalMode) {
        return params.data?.issue_internal_id || '';
    }

    try {
        const clientInfo = JSON.parse(atob(params.data.client_info));

        const decodedInfo = Object.entries(clientInfo)
            .map(([key, value]) => ({ key, value }))
            .filter(item => item.key != null);

        const mainInfoItem = decodedInfo.find(item =>
            normalizeString(item.key) === normalizeString('Dane podstawowe'));

        const mainInfo = mainInfoItem?.value as MainInfoValue;

        const isAssistant = checkAssistantStatus(mainInfo);
        const isMb24Issue = checkMb24Status(mainInfo, decodedInfo);

        const iconHtml = isMb24Issue ? createMb24Badge() :
            (!isAssistant && !isMb24Issue) ? createNoAssistantBadge() : '';

        return iconHtml + (params.data?.issue_internal_id || '');
    } catch (e) {
        console.error('Błąd dekodowania client_info:', e);
        return params.data?.issue_internal_id || '';
    }
}

const baseCellStyle = {
    borderLeft: '1px solid #DBDFF7',
    borderBottom: '1px solid #DBDFF7',
    fontFamily: 'Manrope',
    fontSize: '14px',
    fontWeight: '400',
    letterSpacing: '0.28px'
};

const cellStyles = {
    first: { ...baseCellStyle },
    middle: { ...baseCellStyle },
    last: {
        ...baseCellStyle,
        borderRight: '1px solid #DBDFF7'
    }
};

function getDynamicMinWidth(): number {
    if (window.innerWidth < 768) {
        return 150;
    }
    if (window.innerWidth < 1920) {
        return 300;
    }

    return 400;
}

function compareWaitingTime(valueA, valueB): number {
    if (valueA?.raw !== undefined && valueB?.raw !== undefined) {
        return Number(valueA.raw) - Number(valueB.raw);
    }

    const parseDuration = (value: string): number => {
        if (!value || typeof value !== 'string') return 0;

        const yearsRegex = /(?:(\d+)y)?\s*(?:(\d+)d)?\s*(?:(\d+)h)?\s*(?:(\d+)m)?/;
        const match = value.match(yearsRegex) || [];
        const [years = '0', days = '0', hours = '0', minutes = '0'] = match;

        return Number(years) * 365 * 24 * 60 +
            Number(days)  * 24 * 60 +
            Number(hours) * 60 +
            Number(minutes);
    };

    const strA = valueA?.formatted || valueA;
    const strB = valueB?.formatted || valueB;

    const totalA = parseDuration(strA);
    const totalB = parseDuration(strB);

    return totalA - totalB;
}

/**
 * Sprawdza czy kolumna ma być wyświetlona.
 * @param columns Tablica kolumn do wyświetlenia.
 * @param name Nazwa kolumny.
 * @returns
 */
function isShowColumn(columns: IssueColumn[], name: IssueColumn) {
    return columns.includes(name);
}

/**
 * Funkcja getIssueColumnDefs generuje definicje kolumn dla ag-Grid.
 * Dla kolumny "status" warunkowo dodajemy kolumnę, której nagłówek jest renderowany jako szare kółko
 * (badge), wykorzystując funkcję createStatusBadge(null). Dzięki temu nagłówek nie wyświetla tekstu,
 * a jedynie ikonę (kółko) o domyślnym szarym kolorze.
 *
 * Uwagi do użycia klasy CSS 'custom-ellipsis-cell':
 * - stosowane dla komórek, które mogą mieć długi ciąg znaków
 * - wymagane jest nadpisanie metody 'cellRenderer' w celu dodania kontenera z klasą 'cell-content',
 *   ponieważ sama komórka ma ustawiony 'display: flex', co uniemożliwia użycie `text-overflow: ellipsis`.
 *
 * @param translate         Usługa tłumaczeń, używana do generowania tekstu w nagłówkach.
 * @param durationPipe      Pipe do formatowania czasu oczekiwania.
 * @param datePipe
 * @param openDetails       Callback wywoływany przy kliknięciu w komórkę, otwierający szczegóły sprawy.
 * @param onEmployeeClicked
 * @param onClientClicked   Callback wywoływany przy kliknięciu w komórkę klienta.
 * @param issueDetails      Callback wywoływany przy obsłudze dodatkowych szczegółów sprawy.
 * @param allowedActions    Obiekt konfiguracyjny, określający dostępność akcji (take, delegate, close, open).
 * @param showColumns       Tablica kolumn które mają zostać wyświetlone.
 *
 * @param issueCategoriesService
 * @returns Tablica definicji kolumn (ColDef[]) dla ag-Grid.
 */
export function getIssueColumnDefs(
    translate: TranslateService,
    durationPipe: DurationPipe,
    datePipe: DatePipe,
    openDetails: (event: CellClickedEvent) => void,
    onEmployeeClicked: (event: CellClickedEvent) => void,
    onClientClicked: (event: CellClickedEvent) => void,
    issueDetails: (event: CellClickedEvent) => void,
    allowedActions: { priority: boolean; take: boolean; delegate: boolean; close: boolean; open: boolean },
    showColumns: IssueColumn[],
    issueCategoriesService?: IssueCategoriesService
): ColDef[] {
    return [
        ...(isShowColumn(showColumns, IssueColumn.STATUS) ? [{
            field: IssueColumn.STATUS,
            headerName: '',
            minWidth: 50,
            maxWidth: 50,
            sortable: true,
            suppressHeaderMenuButton: false,
            filter: false,
            tooltipValueGetter: (params) => {
                const statusMap: Record<string, { color: string; text: string }> = {
                    new: {color: getStatusStyles('new').backgroundColor, text: translate.instant('ISSUE-VIEW-SIDEBAR.NEW')},
                    open: {color: getStatusStyles('open').backgroundColor, text: translate.instant('ISSUE-VIEW-SIDEBAR.OPEN')},
                    unaccepted: {color: getStatusStyles('unaccepted').backgroundColor, text: translate.instant('ISSUE-VIEW-SIDEBAR.UNACCEPTED')},
                };
                return statusMap[params.value]?.text || params.value;
            },
            cellRenderer: statusBadgeCellRenderer,
            cellStyle: cellStyles.first
        }] : []),
        ...(isShowColumn(showColumns, IssueColumn.ISSUE_INTERNAL_ID) ? [{
            field: IssueColumn.ISSUE_INTERNAL_ID,
            headerName: 'ID',
            minWidth: 85,
            maxWidth: 90,
            flex: 1,
            sortable: true,
            suppressHeaderMenuButton: false,
            onCellClicked: openDetails,
            cellRenderer: isShowColumn(showColumns, IssueColumn.STATUS) ? MiddleClickCellRendererComponent : triangleBadgeCellRenderer,
            sort: null,
            filter: false,
            cellStyle: cellStyles.first,
            valueGetter: params => Number(params.data.issue_internal_id)
        }] : []),
        ...(isShowColumn(showColumns, IssueColumn.NAME) ? [{
            field: IssueColumn.NAME,
            headerName: translate.instant('ISSUE-TABLE.NAME'),
            minWidth: getDynamicMinWidth(),
            sortable: true,
            onCellClicked: openDetails,
            flex: 10,
            cellRenderer: IssueNameRendererComponent,
            cellClass: 'custom-ellipsis-cell',
            cellStyle: {
                ...cellStyles.middle,
                display: 'block',
                'text-overflow': 'ellipsis',
                'white-space': 'nowrap',
                'overflow': 'hidden'
            },
        }] : []),
        ...(isShowColumn(showColumns, IssueColumn.CLIENT) ? [{
            headerName: translate.instant('ISSUE-TABLE.CLIENT'),
            field: IssueColumn.CLIENT,
            minWidth: 240,
            flex: 1,
            sortable: false,
            cellClass: 'custom-ellipsis-cell',
            filter: false,
            cellStyle: cellStyles.middle,
            onCellClicked: onClientClicked,
            valueGetter: (params: ValueGetterParams) =>
                `${params.data?.first_name || ''} ${params.data?.last_name || ''}`,
            // cellRenderer potrzebny jest dla działania custom-ellipsis-cell (patrz komentarz "getIssueColumnDefs")
            cellRenderer: (params) => {
                return `<div class="cell-content">${params.value}<div>`;
            },
        }] : []),
        ...(isShowColumn(showColumns, IssueColumn.EMPLOYEE) ? [{
            headerName: translate.instant('ISSUE-TABLE.EMPLOYEE'),
            field: IssueColumn.EMPLOYEE,
            minWidth: 220,
            flex: 1,
            sortable: false,
            cellClass: 'custom-ellipsis-cell',
            onCellClicked: onEmployeeClicked,
            filter: false,
            cellStyle: cellStyles.middle,
            cellRenderer: UserCellRendererComponent,
        }] : []),
        ...(isShowColumn(showColumns, IssueColumn.LAST_ACTIVITY) ? [{
            headerName: translate.instant('ISSUE-TABLE.LAST-ACTIVITY'),
            field: IssueColumn.LAST_ACTIVITY,
            minWidth: 160,
            flex: 1,
            sortable: true,
            cellStyle: cellStyles.middle,
            valueGetter: (params: ValueGetterParams) =>
                datePipe.transform(params.data?.modified, 'yyyy-MM-dd HH:mm')
        }] : []),
        ...(isShowColumn(showColumns, IssueColumn.WAITING_TIME) ? [{
            field: IssueColumn.WAITING_TIME,
            headerName: translate.instant('ISSUE-TABLE.WAITING-TIME'),
            sortable: true,
            minWidth: 130,
            flex: 1,
            filter: false,
            cellStyle: cellStyles.middle,
            valueGetter: (params: ValueGetterParams) => {
                const formattedValue = durationPipe.transform(params.data?.waiting_time);
                return {
                    formatted: formattedValue,
                    raw: params.data?.waiting_time
                };
            },
            valueFormatter: (params) => params.value?.formatted || '',
            comparator: compareWaitingTime,
            sort: null
        }] : []),
        ...(isShowColumn(showColumns, IssueColumn.CATEGORY) ? [{
            field: IssueColumn.CATEGORY,
            headerName: translate.instant('ISSUE-TABLE.CATEGORY'),
            minWidth: 160,
            maxWidth: 160,
            flex: 1,
            onCellClicked: openDetails,
            sortable: true,
            suppressHeaderMenuButton: false,
            sort: null,
            filter: false,
            cellStyle: cellStyles.middle,
            cellRenderer: function (params) {
                const wrapper = document.createElement('div');

                const cellElement = document.createElement('span');
                wrapper.appendChild(cellElement);

                cellElement.textContent = params.value || '';

                wrapper.addEventListener('auxclick', (event) => {
                    if (event.button === 1) {
                        event.preventDefault();
                        window.open(`/issues/${params.data.id}`, '_blank');
                    }
                });

                if (params.data && params.data.category_id && issueCategoriesService) {
                    issueCategoriesService.getCategoryName(params.data.category_id)
                        .subscribe(categoryName => {
                            cellElement.textContent = categoryName || params.value || '';
                        });
                }

                return wrapper;
            }
        }] : []),
        ...(isShowColumn(showColumns, IssueColumn.TAGS) ? [{
            field: IssueColumn.TAGS,
            headerName: translate.instant('TAGS.TAGS'),
            minWidth: 270,
            flex: 1,
            sortable: false,
            filter: false,
            cellStyle: cellStyles.middle,
            cellRenderer: IssueTagsRendererComponent
        }] : []),
        {
            headerName: '',
            sortable: false,
            filter: false,
            minWidth: 50,
            maxWidth: 50,
            flex: 1,
            cellStyle: cellStyles.last,
            cellRenderer: IssueMoreOptionsRendererComponent,
            cellRendererParams: {
                issueDetails: issueDetails,
                allowedActions: allowedActions
            },
        }
    ];
}
