import {ColDef, CellClickedEvent, ValueGetterParams} from 'ag-grid-community';
import {TranslateService} from '@ngx-translate/core';
import {MailMoreOptionsRendererComponent} from '../components/more-options-renderer/mail-more-options-renderer.component';
import dayjs from 'dayjs';
import {MailTagsRendererComponent} from '../components/issue-tags-renderer/mail-tags-renderer.component';
import {MailActions} from '../interfaces/mail-actions';
import {MailColumn} from '../common/enums/mail.enum';
import {EmailMessageInterface} from '../common/interfaces/email-message-interface';

const baseCellStyle = {
    borderLeft: '1px solid #DBDFF7',
    borderBottom: '1px solid #DBDFF7',
    fontFamily: 'Manrope',
    fontSize: '14px',
    fontWeight: '400',
    letterSpacing: '0.28px'
};

const cellStyles = {
    first: { ...baseCellStyle },
    middle: { ...baseCellStyle },
    last: {
        ...baseCellStyle,
        borderRight: '1px solid #DBDFF7'
    }
};

function getDynamicMinWidth(): number {
    if (window.innerWidth < 768) {
        return 150;
    }

    if (window.innerWidth < 1920) {
        return 300;
    }

    return 400;
}

/**
 * Sprawdza, czy kolumna ma być wyświetlona.
 * @param columns Tablica kolumn do wyświetlenia.
 * @param name Nazwa kolumny.
 * @returns
 */
function isShowColumn(columns: MailColumn[], name: MailColumn) {
    return columns.includes(name);
}

function hasAction(allowedActions: MailActions) {
    return Object.values(allowedActions).some(value => value === true);
}

/**
 * Funkcja getMailColumnDefs generuje definicje kolumn dla ag-Grid.
 *
 * @param translate
 * @param openDetails
 * @param mailDetails
 * @param allowedActions
 * @param listName
 * @param showColumns
 * @param openClientDetailsByEmail
 * @returns Tablica definicji kolumn (ColDef[]) dla ag-Grid.
 */
export function getMailColumnDefs(
    translate: TranslateService,
    openDetails: (event: CellClickedEvent) => void,
    mailDetails: (event: CellClickedEvent) => void,
    allowedActions: MailActions,
    listName: string,
    showColumns: MailColumn[],
    openClientDetailsByEmail?: (mailData: EmailMessageInterface) => void
): ColDef[] {
    let dateName = translate.instant('MAIL-TABLE.DATE');

    const client = ['draft', 'sent'].includes(listName) ? translate.instant('MAIL-TABLE.RECEIVER') : translate.instant('MAIL-TABLE.SENDER');

    switch (listName) {
        case 'sent':
            dateName = translate.instant('MAIL-TABLE.DATE-SENT');

            break;
        case 'draft':
            dateName = translate.instant('MAIL-TABLE.DATE-CREATED');

            break;
    }

    const columns: ColDef[] = [
        ...(isShowColumn(showColumns, MailColumn.MAIL_ADDRESS) ? [{
            headerName: client,
            field: 'mail_address',
            minWidth: 250,
            flex: 1,
            sortable: true,
            filter: false,
            cellClass: (params) => params.data?.client_id && +params.data.client_id > 0 ? 'custom-ellipsis-cell' : '',
            cellStyle: cellStyles.middle,
            onCellClicked: (params) => params.data?.client_id && +params.data.client_id > 0 && openClientDetailsByEmail ? openClientDetailsByEmail(params.data) : null
        }] : []),
        ...(isShowColumn(showColumns, MailColumn.MAIL_SUBJECT) ? [{
            field: 'mail_subject',
            headerName: translate.instant('MAIL-TABLE.SUBJECT'),
            minWidth: getDynamicMinWidth(),
            sortable: true,
            cellClass: 'custom-ellipsis-cell',
            onCellClicked: openDetails,
            flex: 3,
            cellStyle: (params: any) => {
                const baseStyle = cellStyles.middle;

                if (params.data && +params.data.priority) {
                    return {
                        ...baseStyle,
                        'background-image': `url("data:image/svg+xml;utf8,${encodeURIComponent(`
                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 64 64' width='1em' height='1em'>
                        <g>
                            <path d="M36.989 42.439H27.01L23 2h18z" fill="#b22222"></path>
                            <ellipse cx="31.999" cy="54.354" rx="7.663" ry="7.646" fill="#b22222"></ellipse>
                        </g>
                    </svg>
                `)}")`,
                        'background-repeat': 'no-repeat',
                        'background-position': '0 center',
                        'background-size': '1em 1em',
                        'padding-left': '1.2em',
                        'font-weight': +params.data.is_read === 0 ? 'bold' : 'normal'
                    };
                }

                return {
                    ...baseStyle,
                    'font-weight': +params.data.is_read === 0 ? 'bold' : 'normal'
                };
            }
        }] : []),
        ...(isShowColumn(showColumns, MailColumn.CREATED) ? [{
            field: 'created',
            headerName: dateName,
            sortable: true,
            minWidth: 100,
            flex: 1,
            filter: false,
            cellStyle: cellStyles.last,
            valueGetter: (params: ValueGetterParams) => {
                if (params.data?.timestamp) {
                    return dayjs.unix(+params.data.timestamp).format('DD MMMM YYYY HH:mm');

                } else if (params.data?.created) {
                    return dayjs(params.data.created).format('DD MMMM YYYY HH:mm');
                }

                return '';
            }
        }] : []),
        ...(isShowColumn(showColumns, MailColumn.RECEIVER_MAIL_ADDRESS) ? [{
            headerName: translate.instant('MAIL-TABLE.RECEIVER'),
            field: 'receiver_mail_address',
            minWidth: 250,
            flex: 1,
            sortable: true,
            filter: false,
            cellStyle: cellStyles.middle
        }] : []),
        ...(isShowColumn(showColumns, MailColumn.TAGS) ? [{
            field: 'tags',
            headerName: translate.instant('TAGS.TAGS'),
            minWidth: 300,
            flex: 1,
            sortable: false,
            filter: false,
            cellStyle: cellStyles.middle,
            cellRenderer: MailTagsRendererComponent
        }] : []),
        ...(hasAction(allowedActions) ? [{
            headerName: '',
            sortable: false,
            filter: false,
            minWidth: 50,
            maxWidth: 50,
            flex: 1,
            cellStyle: cellStyles.last,
            cellRenderer: MailMoreOptionsRendererComponent,
            cellRendererParams: {
                mailDetails: mailDetails,
                allowedActions: allowedActions,
                openDialog: openDetails
            },
        }] : [])

    ];

    return columns;
}
