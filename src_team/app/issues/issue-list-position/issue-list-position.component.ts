import {Component, EventEmitter, inject, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges} from '@angular/core';
import {IssueInterface} from '../../common/interfaces/issue.interface';
import {IssueSentenceService} from '../../services/issue-sentence.service';
import {filter, first, map, switchMapTo} from 'rxjs/operators';
import {IssueService} from '../../services/issue.service';
import {HttpErrorResponse} from '@angular/common/http';
import {MatDialog} from '@angular/material/dialog';
import {AuthService} from '../../services/auth/auth.service';
import {Subscription} from 'rxjs';
import {BreakpointObserver, Breakpoints} from '@angular/cdk/layout';
import {SocketIssueLockService} from '../../services/sockets/socket-issue-lock.service';
import {ConfirmDialogComponent} from '../../shared/confirm-dialog/confirm-dialog.component';
import {IssueColumnName} from '../../common/types/issue-columns-name';
import {IsMobileService} from '../../services/is-mobile.service';
import {UserProfileDialogComponent} from '../../users-sidebar/user-profile-dialog/user-profile-dialog.component';
import {IssueInitiatorService} from '../../services/issue-initiator.service';
import {UserStoreService} from '../../services/store/user-store.service';
import {PaymentStatusType} from '../../common/types/payment-status-type';
import {IssueCategoriesService} from '../../services/issue-categories.service';
import {IssueSuggestModalComponent} from '../issue-suggest-modal/issue-suggest-modal.component';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import {MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelDescription, MatExpansionPanelHeader, MatExpansionPanelTitle} from '@angular/material/expansion';
import {DefaultClassDirective, DefaultShowHideDirective} from 'ngx-flexible-layout/extended';
import {DefaultFlexDirective, DefaultLayoutAlignDirective, DefaultLayoutDirective} from 'ngx-flexible-layout/flex';
import {AsyncPipe, NgClass, NgIf} from '@angular/common';
import {MatTooltip} from '@angular/material/tooltip';
import {MatIcon} from '@angular/material/icon';
import {MatChip} from '@angular/material/chips';
import {ClipboardModule} from 'ngx-clipboard';
import {RouterLink} from '@angular/router';
import {TagsComponent} from '../../shared/tags/tags.component';
import {MatCardContent} from '@angular/material/card';
import {MatButton} from '@angular/material/button';
import {IssueActionButtonDirective} from '../directives/issue-action-button.directive';
import {DateDiffFromNowPipe} from '../../shared/pipes/date-diff-from-now.pipe';
import {CheckPermissionNamePipe} from '../../shared/pipes/check-permission-name.pipe';
import {DurationPipe} from '../../shared/pipes/duration.pipe';
import {TruncateHtmlPipe} from '../../shared/pipes/truncate-html.pipe';
import {DateRespondoFormatPipe} from '../../shared/pipes/date-respondo-format.pipe';
import {TruncateStringPipe} from '../../shared/pipes/truncate-string.pipe';
import {AlertService} from '../../services/alert.service';
import {AlertType} from '../../common/enums/alert-type.enum';
import {AlertDuration} from '../../common/enums/alert-duration.enum';

@Component({
    selector: 'app-issue-list-position',
    templateUrl: './issue-list-position.component.html',
    styleUrls: ['./issue-list-position.component.scss'],
    imports: [MatExpansionPanel, DefaultClassDirective, MatExpansionPanelHeader, DefaultLayoutDirective, NgClass, NgIf, MatTooltip, MatExpansionPanelTitle, DefaultFlexDirective, DefaultLayoutAlignDirective, MatIcon, MatChip, ClipboardModule, DefaultShowHideDirective, RouterLink, TagsComponent, MatExpansionPanelDescription, MatCardContent, MatExpansionPanelActionRow, MatButton, IssueActionButtonDirective, AsyncPipe, DateDiffFromNowPipe, CheckPermissionNamePipe, DurationPipe, TruncateHtmlPipe, DateRespondoFormatPipe, TruncateStringPipe, TranslatePipe]
})
export class IssueListPositionComponent implements OnInit, OnChanges, OnDestroy {

    constructor(
        private issueSentenceService: IssueSentenceService,
        private issueService: IssueService,
        private authService: AuthService,
        private userStoreService: UserStoreService,
        private breakpointObserver: BreakpointObserver,
        private dialogRef: MatDialog,
        private socketIssueLockService: SocketIssueLockService,
        private dialog: MatDialog,
        public isMobileService: IsMobileService,
        private issueInitiatorService: IssueInitiatorService,
        private issueCategoryService: IssueCategoriesService,
        private translate: TranslateService
    ) {
    }
    @Input()
    issue: IssueInterface;

    @Input()
    index: number;

    @Input()
    shownColumns: IssueColumnName[] = [];

    @Input()
    showClientName = true;

    @Input()
    hideActions = false;

    @Input()
    expand = false;

    @Input()
    entryList = '';

    @Input()
    disableOpenUser = false;

    @Input()
    valuateIssuePaymentPermission = false;

    @Input()
    showTagsInCollapsedView = true;

    @Input()
    tagsEditMode = true;

    @Input()
    internalMode = false;

    @Input() customClass: string;

    @Input() modal: boolean = false;

    @Output()
    changeIssue = new EventEmitter();

    @Output()
    openIssue: EventEmitter<number> = new EventEmitter();

    issueSentenceBody;
    issueSentenceFilesNumber: number;
    issueInitiatorName;
    issueInitiatorId: number;
    isMb24Issue = false;

    isAssistant = false;
    corrected: number;
    userId;
    owner;
    subscriptions = new Subscription();
    issueLockedByUser = false;
    issueJoinedByUser = false;
    isHandsetTablet: boolean;
    tooltipCopying = false;
    showClientNameInHeader = true;
    categoryName: string;

    public suggestCategory: boolean = false;

    private alertService: AlertService = inject(AlertService);

    private setSocketSubscriptions() {
        this.subscriptions.add(
            this.socketIssueLockService.issueLockSubject
                .pipe(
                    filter(data => +data.issueId === +this.issue.id),
                    filter(data => ['lock', 'unlock', 'issueLockStatus'].includes(data.type))
                )
                .subscribe(data => {
                    switch (data.type) {
                        case 'unlock':
                            this.issueLockedByUser = false;

                            break;
                        case 'lock':
                            this.issueLockedByUser = true;

                            break;
                        case 'issueLockStatus':
                            this.issueLockedByUser = data.userId !== null;

                            break;
                    }
                })
        );
    }

    private setInitiatorChangeSubscription() {
        this.subscriptions.add(
            this.issueInitiatorService.refreshIssueInitiator.subscribe(initiatorId => {
                if (initiatorId === this.issueInitiatorId) {
                    this.fetchIssueSentencesWithInitiators();
                }
            })
        );
    }

    private joinIssueLockSocket() {
        this.socketIssueLockService.setInitialLockStatusListener(this.issue.id);
        this.socketIssueLockService.join(this.issue.id);
        this.issueJoinedByUser = true;
    }

    private leaveIssueLockSocket() {
        if (this.issueJoinedByUser) {
            this.issueJoinedByUser = false;
        }
    }

    public openSuggestCategoryChange(): void {
        this.dialog.open(IssueSuggestModalComponent, {
            width: '500px',
            data: this.issue
        }).afterClosed()
            .subscribe(change => {
                this.issueService.changeIssueType(this.issue.id, change)
                    .pipe(
                        first()
                    )
                    .subscribe(() => {
                        //mtodo
                    });
            });
    }


    private fetchIssueSentencesWithInitiators() {
        this.issueSentenceService.getIssueSentencesWithInitiators('issue_id=' + this.issue.id + '&speaker=initiator&status=sent&order=modified|DESC&limit=1')
            .pipe(filter(issueSentence => issueSentence.length > 0))
            .subscribe(issueSentence => {
                this.issueInitiatorName = issueSentence[0].IssueInitiator.first_name;
                this.issueInitiatorId = +issueSentence[0].IssueInitiator.id;
                this.issueSentenceFilesNumber = issueSentence[0].CommonFileObject.length;
                this.issueSentenceBody = issueSentence[0].IssueSentence.body;
            });
    }

    private setShowClientNameInHeader() {
        if (['delegated', 'unaccepted'].includes(this.entryList)) {
            this.subscriptions.add(
                this.isMobileService.isTabletView.subscribe(
                    isTabletView => this.showClientNameInHeader = !isTabletView
                )
            );
        }
    }

    ngOnInit() {
        const experimentalModeStorage =  localStorage.getItem('userSettings.experimental.suggestCategory');

        if (experimentalModeStorage) {
            this.suggestCategory = JSON.parse(experimentalModeStorage);
        }

        this.setShowClientNameInHeader();
        this.setSocketSubscriptions();
        this.setInitiatorChangeSubscription();

        this.breakpointObserver.observe([Breakpoints.Handset, Breakpoints.Tablet])
            .pipe(map(result => result.matches))
            .subscribe(value => this.isHandsetTablet = value);

        this.userId = this.authService.getUserId();
        this.corrected = +this.issue.corrected;
        this.issueCategoryService.getCategoryName(+this.issue.category_id).subscribe(categoryName => this.categoryName = categoryName ? categoryName : '');
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (+this.issue.owner_id > 0) {
            this.getOwnerName(this.issue.owner_id);
        }

        this.controlBtnState();
    }

    onOpenAccordion() {
        this.openIssue.emit(this.index);
        this.joinIssueLockSocket();
        this.fetchIssueSentencesWithInitiators();
    }

    onCloseAccordion() {
        this.leaveIssueLockSocket();
    }

    clickDetails() {
        if (this.hideActions) {
            this.dialogRef.closeAll();
        }
    }

    clickClose() {
        const confirm$ = this.dialog.open(
            ConfirmDialogComponent,
            {
                width: '400px',
                data: {
                    header: this.translate.instant('ISSUE-LIST-POSITION.CLOSE-CONFIRM')
                },
                panelClass: 'full-width-dialog'
            }
        ).afterClosed().pipe(filter(result => !!result));

        confirm$.pipe(
            switchMapTo(this.issueService.closeIssue(this.issue.id))
        ).subscribe(
            () => {
                this.alertService.showAlert(this.translate.instant('ISSUE-LIST-POSITION.CLOSE-SUCCESS', {issueNumber: this.issue.id}), AlertType.SUCCESS, AlertDuration.MEDIUM);
                this.issue.status = 'closed';
                this.changeIssue.emit(true);
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.alertService.showAlert(this.translate.instant('ISSUE-LIST-POSITION.CLOSE-FAILURE', {issueNumber: this.issue.id}), AlertType.ERROR, AlertDuration.MEDIUM);
                console.error(httpErrorResponse.error.errorMessages);
            }
        );
    }

    clickOpen() {
        const confirm$ = this.dialog.open(
            ConfirmDialogComponent,
            {
                width: '400px',
                data: {
                    header: this.translate.instant('ISSUE-LIST-POSITION.OPEN-CONFIRM')
                },
                panelClass: 'full-width-dialog'
            }
        ).afterClosed().pipe(filter(result => !!result));

        confirm$.pipe(
            switchMapTo(this.issueService.openIssue(this.issue.id))
        ).subscribe(
            () => {
                this.alertService.showAlert(this.translate.instant('ISSUE-LIST-POSITION.OPEN-SUCCESS', {issueNumber: this.issue.id, issueSubject: this.issue.subject}), AlertType.SUCCESS, AlertDuration.MEDIUM);
                this.issue.status = 'open';
                this.changeIssue.emit(true);
            },
            (httpErrorResponse: HttpErrorResponse) => {
                this.alertService.showAlert( this.translate.instant('ISSUE-LIST-POSITION.OPEN-SUCCESS', {issueNumber: this.issue.id, issueSubject: this.issue.subject}), AlertType.ERROR, AlertDuration.MEDIUM);
                console.error(httpErrorResponse.error.errorMessages);
            }
        );
    }

    clickDelegate(event) {
        this.issueService.startDelegateMode(this.issue.id);

        event.stopPropagation();
    }

    clickTake() {
        const confirm$ = this.dialog.open(
            ConfirmDialogComponent,
            {
                width: '400px',
                data: {
                    header: this.translate.instant('ISSUE-LIST-POSITION.ASSIGN-CONFIRM')
                },
                panelClass: 'full-width-dialog'
            }
        ).afterClosed().pipe(filter(result => !!result));

        confirm$.pipe(
            switchMapTo(this.issueService.setOwnership(this.issue.id, this.userId))
        ).subscribe(result => {
                if (!result || result.status !== 'OK') {
                    return;
                }

                this.issue.owner_id = this.userId;
                this.changeIssue.emit(true);
                this.alertService.showAlert(this.translate.instant('ISSUE-LIST-POSITION.ASSIGN-SUCCESS', {issueNumber: this.issue.id}), AlertType.SUCCESS, AlertDuration.MEDIUM);
            },
            () => this.alertService.showAlert(this.translate.instant('ISSUE-LIST-POSITION.ASSIGN-FAILURE', {issueNumber: this.issue.id}), AlertType.ERROR, AlertDuration.MEDIUM)
        );
    }

    getStatusText(status) {
        return this.issueService.getStatusText(status);
    }

    getOwnerName(userId) {
        this.userStoreService.getUserFromStoreTake1(+userId).subscribe(userData => {
            if (userData && userData.User) {
                this.owner = {firstName: userData.User.firstname, lastName: userData.User.lastname};
            }
        });
    }

    ngOnDestroy() {
        this.leaveIssueLockSocket();
        this.subscriptions?.unsubscribe();
    }

    openUser(event: KeyboardEvent) {
        if (!this.disableOpenUser) {
            event.stopImmediatePropagation();

            this.dialog.open(UserProfileDialogComponent, {
                width: '690px',
                height: '620px',
                data: this.issue.owner_id,
                autoFocus: false,
                panelClass: 'full-width-dialog'
            });
        }
    }

    onIssueIdClick($event) {
        $event.stopImmediatePropagation();
        this.tooltipCopying = true;

        setTimeout(() => {
            this.tooltipCopying = false;
        }, 400);
    }

    getPaymentStatusDescription(status: PaymentStatusType) {
        switch (status) {
            case 'pending':
                return this.translate.instant('ISSUE-LIST-POSITION.PAYMENT-STATUS-PENDING');
            case 'success':
                return this.translate.instant('ISSUE-LIST-POSITION.PAYMENT-STATUS-SUCCESS');
            case 'failure':
                return this.translate.instant('ISSUE-LIST-POSITION.PAYMENT-STATUS-FAILURE');
            default:
                return this.translate.instant('ISSUE-LIST-POSITION.PAYMENT-STATUS-READY');
        }
    }

    showTagsIcons() {
        return this.showTagsInCollapsedView && this.issue.tags && this.issue.tags.slice(1, -1);
    }

    getClasses() {
        return {
            [this.customClass]: true,
            'modal-style': this.modal
        };
    }

    private toggleCloseIssueVisibility(show: boolean) {
        const issues = document.querySelectorAll('.close-issue');
        issues.forEach(issue => {
            issue.classList.toggle('d-none', !show);
        });
    }

    private controlBtnState() {
        const issueOwnerId = +this.issue.owner_id;
        const userId = this.authService.getUserId();

        this.userStoreService.getUserFromStoreTake1(userId).subscribe(user => {
            const isSpecialist = user.User.role.name === 'Specjalista';
            const isTrainee = user.User.role.name === 'Praktykant';
            const isIssueOwner = userId === issueOwnerId;

            const shouldHideCloseIssue = (isSpecialist || isTrainee) && !isIssueOwner;
            this.toggleCloseIssueVisibility(!shouldHideCloseIssue);
        });
    }
}
