@use '../../../variables' as *;
@use '../../../scss/mixins' as *;

:host ::ng-deep.editor {
    background-color: #fff;
    border-radius: 14px;
    border: 0.5px solid $fiveways-button-text;
    @media (max-width: 1600px) {
        max-height: 56vh;
    }

    & {
        max-height: 62vh;
    }

    .editor-header {
        position: fixed;
        display: flex;
        align-items: center;
        gap: 10px;
        margin: -20px 21px 10px;
        padding: 5px;
        background-color: white;
        border-radius: 4px;

        ngx-avatars {
            ::ng-deep .avatar-container {
                width: 28px !important;
                height: 28px !important;

                .avatar-content {
                    width: 28px !important;
                    height: 28px !important;
                    line-height: 28px !important;
                    font-size: 9px !important;
                    background-color: rgb(22, 160, 133) !important;
                    color: white !important;
                }
            }
        }

        .editor-header-name {
            font-weight: 500;
            color: #1A9267;
        }

        &.is-mobile {
            margin: 0 20px;
        }
    }

    .ql-toolbar {
        border-bottom: 0.5px solid $fiveways-button-text;
        margin: 0 20px;
        padding: 22px 0 12px 0;

        .ql-formats {
            margin-right: 0;
        }
    }

    .ql-editor {
        word-break: break-word;
        font-size: 16px;
        line-height: 25px;
        height: 100% !important;
    }

    .ql-container {
        height: calc(100% - 62px) !important;
    }

    .ql-disabled {
        background: #ececec;
        height: 100% !important;

        .ql-editor {
            cursor: default;
        }
    }

    a.comment {
        background: #ffff00;
        color: #000;
        text-decoration: none;

        &.selected {
            background: #ffa500;
        }
    }

    #use-template-button {
        color: $dark-grey;
        margin-top: -3px;
    }

    #fullscreen-editor-button {
        color: $dark-grey;
        outline: none;
        margin-top: -5px;
    }

    &.is-mobile #fullscreen-editor-button {
        display: none;
    }

    #checklist-button {
        margin-top: -7px;
    }

    #new-comment-button {
        &:hover {
            mat-icon {
                color: #0066CC;
            }
        }

        mat-icon {
            color: #444;
            font-size: 22px;
            margin-top: -1px;
        }
    }

    & {
        height: 100%;
    }

    .editor-content {
        height: 100%;

        > div {
            overflow: hidden;
        }

        > * {
            width: 100%;
        }
    }

    mat-toolbar {
        background: #fff;
        height: 65px;
        width: 96%;
        margin: 0 21px;
        padding: 0;

        .button-row button {
            margin-left: 15px;
        }
    }

    &.is-mobile {
        border: none;
        height: calc(100% - 30px) !important;

        .editor-content > * {
            min-height: 200px;
        }

        .ql-container {
            height: calc(100% - 67px) !important;
        }

        .ql-editor {
            font-size: 14px;
            line-height: 21px;
        }

        mat-toolbar {
            width: 88%;

            .button-row button {
                margin-left: 15px;
            }
        }
    }
}

.button-row button {
    margin-left: 8px;
}

.is-mobile .button-row button {
    margin-left: 10px;
}

.buttons-container {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end !important;
    margin: 10px 0 10px -20px;
    min-height: 51px !important;

    &.space-between {
        width: auto;
        margin: 10px;
        align-items: center;
        justify-content: space-between;

        .ng-star-inserted {
            margin-left: 0;
        }
    }

    @media (max-width: 600px) {
        margin: 0 0 20px -20px;
    }
}

.save-icon:disabled .mat-icon {
    color: $fiveways-gray !important;
}

.issue-waiting {
    font-size: 16px;
}

.icon {
    position: relative;
    top: 4px;
    right: 8px;
}
