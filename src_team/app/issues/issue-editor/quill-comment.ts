import Quill from 'quill';
import InlineBlot from 'quill/blots/inline';

export class CommentBlot extends InlineBlot {
    static blotName = 'comment';
    static className = 'comment';
    static tagName = 'a';

    domNode!: HTMLElement;

    static create(value) {
        const node = super.create();

        node.setAttribute('id', value);
        node.setAttribute('href', ''); // obejście z https://github.com/quilljs/quill/issues/1232#issuecomment-421504506

        return node;
    }

    static formats(node) {
        return node.getAttribute('id') || true;
    }

    format(name, value) {
        if (name === 'comment' && value) {
            this.domNode.setAttribute('id', value);
        } else {
            super.format(name, value);
        }
    }
}

// Register the new blot
Quill.register(CommentBlot);
