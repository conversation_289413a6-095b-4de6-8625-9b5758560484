<div class="new-batch-issues">
    <form [formGroup]="newIssueForm">
        <mat-card class="mat-elevation-z0 panel">
            <mat-card-title>{{ 'ISSUE-ADD-BATCH-VIEW.ADD-ISSUES' | translate }}</mat-card-title>
            <mat-card-content>
                <div class="form-content">
                    <mat-form-field class="full-width">
                        <mat-chip-listbox
                            #chipList
                            formControlName="clientsCtrl"
                            (focusin)="showAllClientsChips = true"
                            (focusout)="onFocusOutClientsCtrl($event)">
                            <ng-container *ngIf="showAllClientsChips || selectedClients.length <= 4; else showAllClientsChipsTmpl">
                                <mat-chip
                                    *ngFor="let selectedClient of selectedClients"
                                    [removable]="true"
                                    (removed)="remove(selectedClient)">
                                    {{ selectedClient.first_name }} {{ selectedClient.last_name }}
                                    <mat-icon matChipRemove>cancel</mat-icon>
                                </mat-chip>
                            </ng-container>
                            <ng-template #showAllClientsChipsTmpl>
                                <mat-chip
                                    *ngFor="let selectedClient of visibleClients"
                                    [removable]="false">
                                    {{ selectedClient.first_name }} {{ selectedClient.last_name }}
                                </mat-chip>
                                <mat-chip style="background-color: #5CB990;">
                                    {{ 'ISSUE-ADD-BATCH-VIEW.AND-MORE' | translate }} {{ selectedClients.length - visibleClients.length }}
                                </mat-chip>
                            </ng-template>
                            <input
                                #searchInput
                                matInput
                                #matAutoComplete="matAutocompleteTrigger"
                                [formControl]="searchCtrl"
                                [matAutocomplete]="auto"
                                [matChipInputFor]="chipList"
                                [matChipInputSeparatorKeyCodes]="separatorKeysCodes" value=""
                                [matChipInputAddOnBlur]="false"
                                (click)="matAutoComplete.openPanel()">
                        </mat-chip-listbox>
                        <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selected($event)">
                            <mat-option *ngFor="let client of filteredClients$ | async" [value]="client">
                                {{ client.first_name }} {{ client.last_name }}
                            </mat-option>
                        </mat-autocomplete>
                        <mat-error *ngIf="clientsCtrl.invalid && (clientsCtrl.dirty || clientsCtrl.touched)">
                            <div *ngIf="clientsCtrl.errors.required">
                                {{ 'ISSUE-ADD-BATCH-VIEW.CLIENT-REQUIRED' | translate }}
                            </div>
                        </mat-error>
                    </mat-form-field>
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'ISSUE-ADD-BATCH-VIEW.ISSUE-TITLE' | translate }}</mat-label>
                        <input matInput formControlName="titleCtrl" autocomplete="off">
                        <mat-error *ngIf="titleCtrl.invalid && (titleCtrl.dirty || titleCtrl.touched)">
                            <div *ngIf="titleCtrl.errors.required">
                                {{ 'ISSUE-ADD-BATCH-VIEW.REQUIRED' | translate }}
                            </div>
                            <div *ngIf="titleCtrl.errors.minlength">
                                {{ 'ISSUE-ADD-BATCH-VIEW.TITLE-REQUIRED' | translate }} {{ titleCtrl.errors.minlength.requiredLength }} {{ 'ISSUE-ADD-BATCH-VIEW.CHARACTERS' | translate }}
                            </div>
                        </mat-error>
                    </mat-form-field>
                </div>
            </mat-card-content>
        </mat-card>

        <div class="editor">
            <div class="editor-content">
                <div class="quill editor">
                    <quill-editor
                            placeholder="{{ 'ISSUE-ADD-BATCH-VIEW.ISSUE-CONTENT' | translate }}"
                            [bounds]="'.quill'"
                            formControlName="bodyCtrl"
                            [formats]="editorFormats"
                            (paste)="onPaste($event)">
                        <div quill-editor-toolbar>
                            <span class="ql-formats">
                                <button class="ql-bold" title="{{ 'ISSUE-ADD-BATCH-VIEW.BOLD' | translate }}"></button>
                                <button class="ql-italic" title="{{ 'ISSUE-ADD-BATCH-VIEW.ITALICS' | translate }}"></button>
                                <button class="ql-underline" title="{{ 'ISSUE-ADD-BATCH-VIEW.UNDERLINE' | translate }}"></button>
                                <button class="ql-strike" title="{{ 'ISSUE-ADD-BATCH-VIEW.CROSSING' | translate }}"></button>
                            </span>
                            <span class="ql-formats">
                                <button class="ql-list" value="bullet" title="{{ 'ISSUE-ADD-BATCH-VIEW.BULLET' | translate }}"></button>
                                <button class="ql-list" value="ordered" title="{{ 'ISSUE-ADD-BATCH-VIEW.NUMBERED' | translate }}"></button>
                                <button class="ql-link" title="{{ 'ISSUE-ADD-BATCH-VIEW.LINK' | translate }}"></button>
                            </span>
                            <span class="ql-formats">
                                <button class="ql-clean" title="{{ 'ISSUE-ADD-BATCH-VIEW.CLEAR' | translate }}"></button>
                            </span>
                        </div>
                    </quill-editor>
                </div>

                <mat-error class="error" *ngIf="bodyCtrl.invalid && (bodyCtrl.dirty || bodyCtrl.touched)">
                    <div *ngIf="bodyCtrl.errors.minChars">
                        {{ 'ISSUE-ADD-BATCH-VIEW.ISSUE-CONTENT' | translate }} {{ bodyCtrl.errors.minChars }} {{ 'ISSUE-ADD-BATCH-VIEW.CHARACTERS' | translate }}
                    </div>
                </mat-error>
                <mat-toolbar class="editor-toolbar mat-elevation-z0">
                    <div
                        class="draft-saved-indicator"
                        [ngClass]="{saved: draftSaved}"
                        matTooltip="{{ draftSaved ? ('ISSUE-ADD-BATCH-VIEW.DRAFT-SAVED' | translate) : ('ISSUE-ADD-BATCH-VIEW.DRAFT-SAVING' | translate) }}"
                    >
                        <mat-icon>save</mat-icon>
                    </div>
                    <ng-container #fileUploaderContainer></ng-container>
                    <div class="spacer"></div>
                    <button
                        mat-stroked-button
                        class="button-rounded primary"
                        color="primary"
                        [disabled]="!newIssueForm.valid || sending"
                        (click)="onSubmit()">
                            <ng-container *ngIf="!sending; else sendingSpinner">
                                <mat-icon>send</mat-icon>
                            </ng-container>
                            <ng-template #sendingSpinner>
                                <mat-spinner diameter="20" color="primary" [ngStyle]="{display: 'inline-block', 'margin-right': '4px'}"></mat-spinner>
                            </ng-template>
                            {{ sending ? ('ISSUE-ADD-BATCH-VIEW.SENDING' | translate) : ('ISSUE-ADD-BATCH-VIEW.SEND' | translate) }}
                    </button>
                </mat-toolbar>
            </div>
        </div>
    </form>
</div>
