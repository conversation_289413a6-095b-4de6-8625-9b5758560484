import { <PERSON>MM<PERSON>, ENTER } from '@angular/cdk/keycodes';
import { Component, ElementRef, OnDestroy, OnInit, SecurityContext, ViewChild, ViewContainerRef } from '@angular/core';
import { AbstractControl, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteSelectedEvent, MatAutocompleteTrigger, MatAutocomplete, MatOption } from '@angular/material/autocomplete';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { EMPTY, Observable, Subscription } from 'rxjs';
import { catchError, debounceTime, map, startWith, switchMap, tap } from 'rxjs/operators';
import { quillConfig } from '../../common/configs/quill-config';
import { ClientPaginationResponse, IssueInitiator } from '../../common/interfaces/issue-initiator.interface';
import { ClientsService } from '../../services/clients.service';
import { RespondoValidators } from '../../shared/validators/RespondoValidators';
import { activateLinks } from '../../common/utils/activate-links';
import { IssueBatchDraftService } from '../../services/issue-batch-draft.service';
import { IssueBatchDraftRequest } from '../../common/interfaces/api/requests/issue-batch-draft-request.interface';
import { ApiResponse } from '../../common/interfaces/api/api-response';
import { IssueBatchDraft, IssueInitiatorsIssueBatchDraft } from '../../common/interfaces/api/responses/issue-batch-draft.interface';
import { TemplateFileUploaderComponent } from '../../shared/template-file-uploader/template-file-uploader.component';
import { FileInputCreationService } from '../../services/file-input-creation.service';
import { AlertType } from '../../common/enums/alert-type.enum';
import { AlertService } from '../../services/alert.service';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import { MatCard, MatCardTitle, MatCardContent } from '@angular/material/card';
import { MatFormField, MatError, MatLabel } from '@angular/material/select';
import { MatChipListbox, MatChip, MatChipRemove, MatChipInput } from '@angular/material/chips';
import { NgIf, NgFor, NgClass, NgStyle, AsyncPipe } from '@angular/common';
import { MatIcon } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { QuillEditorComponent } from 'ngx-quill';
import { MatToolbar } from '@angular/material/toolbar';
import { DefaultClassDirective, DefaultStyleDirective } from 'ngx-flexible-layout/extended';
import { MatTooltip } from '@angular/material/tooltip';
import { MatButton } from '@angular/material/button';
import { MatProgressSpinner } from '@angular/material/progress-spinner';

@Component({
    selector: 'app-issue-add-batch',
    templateUrl: './issue-add-batch-view.component.html',
    styleUrls: ['./issue-add-batch-view.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, MatCard, MatCardTitle, MatCardContent, MatFormField, MatChipListbox, NgIf, NgFor, MatChip, MatIcon, MatChipRemove, MatAutocompleteTrigger, MatChipInput, MatAutocomplete, MatOption, MatError, MatLabel, MatInput, QuillEditorComponent, MatToolbar, NgClass, DefaultClassDirective, MatTooltip, MatButton, MatProgressSpinner, NgStyle, DefaultStyleDirective, AsyncPipe, TranslatePipe]
})
export class IssueAddBatchViewComponent implements OnInit, OnDestroy {

  public newIssueForm: UntypedFormGroup;

  /** Wszyscy klienci. */
  public clients: IssueInitiator[];

  /** Wybrani klienci "adresaci". */
  public selectedClients: IssueInitiator[] = [];

  public get visibleClients(): IssueInitiator[] {
    return this.selectedClients.slice(0, 4);
  }

  public showAllClientsChips: boolean = false;

  public searchCtrl: UntypedFormControl = new UntypedFormControl();

  public separatorKeysCodes: number[] = [ENTER, COMMA];

  /** Przefiltrowani klienci dla "autocomplete". */
  public filteredClients$: Observable<IssueInitiator[]>;

  public editorFormats: string[] = quillConfig.formats;

  public sending: boolean = false;

  public draftSaved: boolean = true;

  private fileUploaderComponentInstance: TemplateFileUploaderComponent;

  private draft: IssueBatchDraft | null = null;

  private subscriptions: Subscription = new Subscription();

  public get clientsCtrl(): AbstractControl | null {
    return this.newIssueForm.get('clientsCtrl');
  }

  public get titleCtrl(): AbstractControl | null {
    return this.newIssueForm.get('titleCtrl');
  }

  public get bodyCtrl(): AbstractControl | null {
    return this.newIssueForm.get('bodyCtrl');
  }

  @ViewChild('searchInput') searchInput: ElementRef<HTMLInputElement>;

  @ViewChild('fileUploaderContainer', {read: ViewContainerRef}) fileUploaderContainer: ViewContainerRef;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly clientsService: ClientsService,
    private readonly issueBatchDraftService: IssueBatchDraftService,
    private readonly fileInputCreationService: FileInputCreationService,
    private readonly alertService: AlertService,
    private readonly translateService: TranslateService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly domSanitizer: DomSanitizer,
  ) {
    this.newIssueForm = this.formBuilder.group({
      clientsCtrl: ['', [Validators.required]],
      titleCtrl: ['', [Validators.minLength(5), Validators.required]],
      bodyCtrl: ['', [RespondoValidators.minChars(10)]],
    });
  }

  ngOnInit(): void {
    this.subscriptions.add(
      this.getClients().subscribe({
        next: () => {
          this.refreshFilterClients();

          this.getDraft();
        }
      })
    );

    this.subscriptions.add(
      this.newIssueForm.valueChanges.pipe(debounceTime(500)).subscribe(() => {
        if (this.draft) {
          this.createFileInputComponent();
        }

        if (this.newIssueForm.touched || this.newIssueForm.dirty) {
          this.draftSaved = false;
          this.updateDraft();
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions?.unsubscribe();
  }

  private createFileInputComponent() {
    if (!this.draft) {
      return;
    }

    this.fileUploaderComponentInstance = this.fileInputCreationService.createFileInputComponent(
        this.fileUploaderContainer, true, 'issueBatchDraft', this.draft?.id
    );
    this.fileUploaderComponentInstance.userType = 'isExpert';
}

  /**
   * Zdarzenie wybóru klienta z "autocomplete".
   * @param event
   */
  public selected(event: MatAutocompleteSelectedEvent): void {
    this.selectedClients.push(event.option.value);
    this.clientsCtrl?.setValue(this.selectedClients);
    this.updateDraft();

    this.searchInput.nativeElement.value = '';

    this.searchCtrl.setValue(null);
  }

  /**
   * Usuwa klienta z kolekcji wybranych klientów.
   * @param selectedClient
   */
  public remove(selectedClient: IssueInitiator): void {
    const index: number = this.selectedClients.indexOf(selectedClient);

    if (index >= 0) {
      this.selectedClients.splice(index, 1);
    }

    this.newIssueForm.patchValue({ clientsCtrl: this.selectedClients });

    // Odśwież przefiltrowanych klientów dla "autocomplete".
    this.refreshFilterClients();
  }

  public onFocusOutClientsCtrl(event: FocusEvent): void {
    const target = event.relatedTarget as HTMLElement;

    if (target && target.closest('mat-chip-list')) {
      return;
    }

    this.showAllClientsChips = false;
  }

  public onPaste(event: ClipboardEvent): void {
    if (event.clipboardData && event.clipboardData.getData) {
      event.preventDefault();

      const clearText = this.domSanitizer.sanitize(SecurityContext.HTML, activateLinks(event.clipboardData.getData('text/plain')));

      const selection = window.getSelection();

      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        range.deleteContents();

        const span: HTMLSpanElement = document.createElement('span');
        span.innerHTML = clearText || '';

        range.insertNode(span);
        range.collapse(false);
      }
    }
  }

  public onSaveDraft(): void {
    this.updateDraft();
  }

  public onSubmit(): void {
    if (!this.draft) {
      return;
    }

    this.sending = true;

    this.subscriptions.add(
      this.issueBatchDraftService.createIssues(this.draft.id).subscribe({
        next: () => {
          this.sending = false;
          this.alertService.showAlert(this.translateService.instant('ISSUE-ADD-BATCH-VIEW.ISSUES-ADDED', {counter: this.selectedClients.length}), AlertType.SUCCESS);
          this.router.navigate(['/issue/list/my']);
        },
        error: () => {
          this.sending = false;
          this.alertService.showAlert(this.translateService.instant('ISSUE-ADD-BATCH-VIEW.ISSUES-ERROR'), AlertType.ERROR);
        }
      })
    );
  }

  /**
   * Pobierz wersję roboczą i zainicjuj formularz.
   * @returns
   */
  private getDraft(): void {
    this.subscriptions.add(
      this.issueBatchDraftService.getDraft().subscribe({
        next: (draftResponse: ApiResponse<IssueBatchDraft>) => {
          // Jeżeli nie istnieje wersja robocza, utwórz nową.
          if (draftResponse.results.length <= 0) {
            this.addDraft();
            return;
          }

          this.draft = draftResponse.results[0].IssueBatchDraft;

          // Inicjuj tylko gdy nie ma wybranych klientów.
          if (this.selectedClients.length === 0) {
            const issueInitiatorIds: number[] = this.draft.issue_initiators.map((ii: IssueInitiatorsIssueBatchDraft) => ii.id);
            this.initSelectedClients(issueInitiatorIds, this.clients);
          } else {
            this.updateDraft();
          }

          this.newIssueForm.patchValue({
            titleCtrl: this.draft.subject,
            bodyCtrl: this.draft.body,
          });
        }
      })
    );
  }

  /**
   * Zainicjuj klientów do wysłania sprawy. Zainicjuj tylko tych klientów, którzy są dostępni (pobrani z API).
   * @param issueInitiatorsIds Identyfikatory klientów, którzy mają być zainicjowani do wysłania sprawy.
   * @param clients Lista dostępnych klientów (pobrani z API).
   */
  private initSelectedClients(issueInitiatorsIds: number[], clients: IssueInitiator[]): void {
    this.selectedClients = [];

    for (const client of clients) {
      if (issueInitiatorsIds?.includes(client.id ? +client.id : 0)) {
        this.selectedClients.push(client);
      }
    }

    this.clientsCtrl?.setValue(this.selectedClients);
  }

  /**
   * Dodaj wersję roboczą.
   */
  private addDraft(): void {
    const draft: IssueBatchDraftRequest = {
      issue_initiators: this.selectedClients.map(client => ({
        id: client.id ? +client.id : 0,
        user_client_id: client.user_client_id ? +client.user_client_id : 0,
      })),
      subject: this.titleCtrl?.value,
      body: this.bodyCtrl?.value,
    };

    this.subscriptions.add(
      this.issueBatchDraftService.addDraft(draft).subscribe({
        next: (res) => {
          this.draft = res;

          this.draftSaved = true;
        }
      })
    );
  }

  /**
   * Zaktualizuj wersję roboczą.
   * @returns
   */
  private updateDraft(): void {
    // Jeżeli nie ma wersji roboczej, dodaj nową.
    if (!this.draft) {
      this.addDraft();
      return;
    }

    const draft: IssueBatchDraftRequest = {
      issue_initiators: this.selectedClients.map(client => ({
        id: client.id ? +client.id : 0,
        user_client_id: client.user_client_id ? +client.user_client_id : 0,
      })),
      subject: this.titleCtrl?.value,
      body: this.bodyCtrl?.value,
    };

    this.subscriptions.add(
      this.issueBatchDraftService.updateDraft(this.draft.id, draft).subscribe({
        next: () => {
          this.draftSaved = true;
        }
      })
    );
  }

  /**
   * Obserwuj zmiany w kontrolce wyszukiwania klienta.
   * @returns Przefiltrowani klienci.
   */
  private searchCtrlChanges(): Observable<IssueInitiator[]> {
    return this.searchCtrl.valueChanges.pipe(
      startWith(''),
      map((search: string | null): IssueInitiator[]  => {
        return this.filterClients(search);
      })
    );
  }

  /**
   * Pobierz klientów.
   * @returns
   */
  private getClients(): Observable<ClientPaginationResponse> {
    return this.route.queryParams.pipe(
      switchMap(params => {
        const clientsIDs: number[] = params['clients']?.map((clientID: string) => +clientID);

        return this.clientsService.getClients('?limit=9999&user_client_id=gt_0').pipe(
          tap((res: ClientPaginationResponse): void => {
            this.clients = res.items;

            this.initSelectedClients(clientsIDs, this.clients);
          }),
          catchError(error => {
            console.error('Wystąpił błąd przy wyświetlaniu listy klientów:', error);
            return EMPTY;
          })
        );
      })
    );
  }

  /**
   * Odświeża kolekcję przefiltrowanych klientów.
   */
  private refreshFilterClients(): void {
    this.filteredClients$ = this.searchCtrlChanges();
  }

  /**
   * Przefiltruj klientów.
   * @param searchTerm
   * @returns
   */
  private filterClients(searchTerm: string | object | null): IssueInitiator[] {
    if (typeof searchTerm === 'object' || !searchTerm || searchTerm.trim() === '') {
        return this.clients.filter(client => !this.selectedClients.includes(client));
    }

    const lowerCaseSearchTerm = searchTerm.toLowerCase().trim();

    return this.clients.filter(client =>
        +(client.user_client_id ?? 0) > 0 &&
        (
            client.first_name?.toLowerCase().includes(lowerCaseSearchTerm) ||
            client.last_name?.toLowerCase().includes(lowerCaseSearchTerm) ||
            client.email?.toLowerCase().includes(lowerCaseSearchTerm)
        ) &&
        !this.selectedClients.includes(client)
    );
  }
}
