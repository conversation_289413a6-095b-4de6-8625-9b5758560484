<div [@fadeInOut] class="poll-report-container d-flex col pt-20 pb-40" *ngIf="!showLoader; else loader">
    <ng-container *ngIf="totalAnswers; else noData">
        <ng-container>
            <mat-card *ngIf="devMode">
                totalAnswers: {{totalAnswers}} <br><br>
                answersReport: {{ answersReport | json }}
            </mat-card>
        </ng-container>

        <ng-container>
            <mat-card *ngIf="!isAnonymous">
                <div class="d-flex row justify-between mb-20">
                    <h3 class="poll-report-title pb-15">{{ 'POLL-REPORT.SELECT-RESPONDENT' | translate }}</h3>
                </div>
                <div class="respondent-selection">
                    <mat-form-field class="respondent-selection__field">
                        <mat-label>{{ 'POLL-REPORT.RESPONDENT' | translate }}</mat-label>
                        <mat-select (selectionChange)="onChangeRespondent($event)" [value]="activeRespondent?.id">
                            <ng-container *ngFor="let respondent of respondents">
                                <mat-option [value]="respondent.id" *ngIf="pollType !== 'offer' || respondent.id !== 0">
                                    {{ respondent.email }}
                                    <ng-container *ngIf="respondent.company">
                                        | {{ respondent.company }}
                                    </ng-container>
                                    <ng-container *ngIf="respondent.firstname">
                                        | {{ respondent.firstname }}
                                    </ng-container>
                                    <ng-container *ngIf="respondent.lastname">
                                   {{ respondent.lastname }}
                                    </ng-container>
                                    <ng-container *ngIf="respondent.phone">
                                        | {{ respondent.phone }}
                                    </ng-container>
                                </mat-option>
                            </ng-container>
                        </mat-select>
                    </mat-form-field>
                    <mat-paginator
                            class="mt-10"
                            [pageSize]="1"
                            [length]="respondents.length"
                            [pageIndex]="getRespondentIndex()"
                            (page)="onPageChange($event)">
                    </mat-paginator>
                </div>
                 </mat-card>
        </ng-container>

        <ng-container *ngFor="let report of answersReport">
            <ng-container *ngIf="+report.totalCounter > 0">
                <ng-container *ngIf="(report.type === 'shortQuestion' && report.value) || (report.type === 'longQuestion' && report.value)">
                    <mat-card>
                        <h3 class="poll-report-title pb-10">{{ report.title }}</h3>
                        <p class="poll-report-text">{{ report.value.length }} {{ 'POLL-REPORT.ANSWERS-COUNT' | translate }}</p>
                        <ng-container *ngFor="let answer of report.value; let i = index">
                            <ng-container *ngIf="i < report.showAnswersCount">
                                <mat-form-field appearance="outline" class="full-width">
                                    <ng-container>
                                        <textarea [value]="answer" matInput readonly></textarea>
                                    </ng-container>
                                </mat-form-field>
                            </ng-container>
                        </ng-container>
                        <button mat-button *ngIf="report.value.length > report.showAnswersCount && report.value.length >= 4 else toggle" (click)="showMoreAnswers(report)">
                            {{ 'POLL-REPORT.SHOW-MORE' | translate }}
                        </button>
                        <ng-template #toggle>
                            <button mat-button *ngIf="(report.value.length - report.showAnswersCount) < 4 && report.value.length >= 5" (click)="toggleShowAllAnswers(report)">
                                {{ 'POLL-REPORT.COLLAPSE-ALL' | translate }}
                            </button>
                        </ng-template>
                        <div class="report-buttons">
                            <app-5ways-button
                                [variant]="ButtonVariant.SECONDARY"
                                iconLeft="download"
                                (click)="xlsxExport(report.type, report.title)">
                                {{ 'POLL-REPORT.EXPORT-XLSX' | translate }}
                            </app-5ways-button>
                            <app-5ways-button
                                [variant]="ButtonVariant.SECONDARY"
                                iconLeft="printer"
                                (click)="exportPdf(report.type, report.title)">
                                {{ 'USER-EDIT-REPORT.GENERATE-REPORT' | translate }}
                            </app-5ways-button>
                        </div>
                    </mat-card>
                </ng-container>

                <ng-container *ngIf="activeRespondent?.id !== 0">
                    <mat-card *ngIf="['oneOptionQuestion', 'selectQuestion', 'multipleOptionQuestion', 'lineScaleQuestion'].includes(report.type)">

                        <h3 class="poll-report-title pb-10">{{ report.title }}</h3>

                        <p class="poll-report-text">
                            1 {{ 'POLL-REPORT.PERSON-ANSWERED' | translate }}
                        </p>
                        <ng-container *ngFor="let answer of report.value; let i = index">
                            <ng-container *ngIf="answer.value > 0">
                                <mat-form-field appearance="outline" class="full-width">
                                    <textarea [value]="answer.name" matInput readonly></textarea>
                                </mat-form-field>
                            </ng-container>
                        </ng-container>
                        <button mat-button *ngIf="report.value.length > report.showAnswersCount && report.value.length >= 4 else toggle" (click)="showMoreAnswers(report)">
                            {{ 'POLL-REPORT.SHOW-MORE' | translate }}
                        </button>
                        <ng-template #toggle>
                            <button mat-button *ngIf="(report.value.length - report.showAnswersCount) < 4 && report.value.length >= 5" (click)="toggleShowAllAnswers(report)">
                                {{ 'POLL-REPORT.COLLAPSE-ALL' | translate }}
                            </button>
                        </ng-template>
                    </mat-card>

                    <mat-card *ngIf="['gridOneQuestion', 'gridQuestion'].includes(report.type)">
                        <h3 class="poll-report-title pb-10">{{ report.title }}</h3>

                        <p class="poll-report-text">
                            1 {{ 'POLL-REPORT.PERSON-ANSWERED' | translate }}
                        </p>
                        <ng-container *ngFor="let row of report.value; let i = index">
                            <ng-container *ngFor="let series of row.series">
                                <ng-container *ngIf="series.value > 0">
                                    <mat-form-field appearance="outline" class="full-width">
                                        <textarea [value]="row.name + ': ' + series.name" matInput readonly></textarea>
                                    </mat-form-field>
                                </ng-container>
                            </ng-container>
                        </ng-container>
                        <button mat-button *ngIf="report.value.length > report.showAnswersCount && report.value.length >= 4 else toggle" (click)="showMoreAnswers(report)">
                            {{ 'POLL-REPORT.SHOW-MORE' | translate }}
                        </button>
                        <ng-template #toggle>
                            <button mat-button *ngIf="(report.value.length - report.showAnswersCount) < 4 && report.value.length >= 5" (click)="toggleShowAllAnswers(report)">
                                {{ 'POLL-REPORT.COLLAPSE-ALL' | translate }}
                            </button>
                        </ng-template>
                    </mat-card>
                </ng-container>

                <ng-container *ngIf="activeRespondent?.id === 0">
                    <ng-container *ngIf="report.type === 'oneOptionQuestion' || report.type === 'selectQuestion'">
                        <mat-card>
                            <h3>{{ report.title }}</h3>
                            <p> {{ report.totalCounter }}
                                <ng-container *ngIf="report.totalCounter === 1; else pluralResponse">{{ 'POLL-REPORT.ANSWER' | translate }}</ng-container>
                                <ng-template #pluralResponse>{{ 'POLL-REPORT.ANSWERS' | translate }}</ng-template>
                                {{ 'POLL-REPORT.PEOPLE-ANSWERED' | translate }}
                            </p>
                            <ngx-charts-pie-chart
                                    [results]="report.value"
                                    [legend]="showLegend"
                                    [scheme]="colorScheme"
                                    [legendPosition]="legendPosition"
                                    [labels]="showLabels"
                                    [trimLabels]="true"
                                    [maxLabelLength]="maxLabelLength()"
                                    [view]="getView()"
                            >
                            </ngx-charts-pie-chart>
                            <div class="report-buttons">
                                <app-5ways-button
                                    [variant]="ButtonVariant.SECONDARY"
                                    iconLeft="download"
                                    (click)="xlsxExport(report.type, report.title)">
                                    {{ 'POLL-REPORT.EXPORT-XLSX' | translate }}
                                </app-5ways-button>
                                <app-5ways-button
                                    [variant]="ButtonVariant.SECONDARY"
                                    iconLeft="printer"
                                    (click)="exportPdf(report.type, report.title)">
                                    {{ 'USER-EDIT-REPORT.GENERATE-REPORT' | translate }}
                                </app-5ways-button>
                            </div>
                        </mat-card>
                    </ng-container>

                    <ng-container *ngIf="report.type === 'multipleOptionQuestion'">
                        <mat-card>
                            <h3>{{ report.title }}</h3>
                            <p> {{ report.totalCounter }}
                                <ng-container *ngIf="report.totalCounter === 1; else pluralResponse">{{ 'POLL-REPORT.ANSWER' | translate }}</ng-container>
                                <ng-template #pluralResponse>{{ 'POLL-REPORT.ANSWERS' | translate }}</ng-template>
                                {{ 'POLL-REPORT.PEOPLE-ANSWERED' | translate }}
                            </p>
                            <ngx-charts-bar-horizontal
                                    [results]="report.value"
                                    [yAxis]="true"
                                    [xAxis]="true"
                                    [legend]="showLegend"
                                    [legendPosition]="legendPosition"
                                    [scheme]="colorScheme"
                                    [showXAxisLabel]="true"
                                    [showYAxisLabel]="true"
                                    [yAxisTickFormatting]="yAxisTickFormatting"
                                    [xAxisTickFormatting]="yAxisTickFormatting"
                                    [view]="getView()"
                            >
                            </ngx-charts-bar-horizontal>
                            <div class="report-buttons">
                                <app-5ways-button
                                    [variant]="ButtonVariant.SECONDARY"
                                    iconLeft="download"
                                    (click)="xlsxExport(report.type, report.title)">
                                    {{ 'POLL-REPORT.EXPORT-XLSX' | translate }}
                                </app-5ways-button>
                                <app-5ways-button
                                    [variant]="ButtonVariant.SECONDARY"
                                    iconLeft="printer"
                                    (click)="exportPdf(report.type, report.title)">
                                    {{ 'USER-EDIT-REPORT.GENERATE-REPORT' | translate }}
                                </app-5ways-button>
                            </div>
                        </mat-card>
                    </ng-container>

                    <ng-container *ngIf="report.type === 'lineScaleQuestion'">
                        <mat-card>
                            <h3>{{ report.title }}</h3>
                            <p> {{ report.totalCounter }}
                                <ng-container *ngIf="report.totalCounter === 1; else pluralResponse">{{ 'POLL-REPORT.ANSWER' | translate }}</ng-container>
                                <ng-template #pluralResponse>{{ 'POLL-REPORT.ANSWERS' | translate }}</ng-template> {{ 'POLL-REPORT.PEOPLE-ANSWERED' | translate }}
                            </p>
                            <ngx-charts-bar-vertical
                                    [results]="report.value"
                                    [scheme]="colorScheme"
                                    [yAxis]="true"
                                    [xAxis]="true"
                                    [legend]="showLegend"
                                    [yAxisTickFormatting]="yAxisTickFormatting"
                                    [view]="getView()"
                            >
                            </ngx-charts-bar-vertical>
                            <div class="report-buttons">
                                <app-5ways-button
                                    [variant]="ButtonVariant.SECONDARY"
                                    iconLeft="download"
                                    (click)="xlsxExport(report.type, report.title)">
                                    {{ 'POLL-REPORT.EXPORT-XLSX' | translate }}
                                </app-5ways-button>
                                <app-5ways-button
                                    [variant]="ButtonVariant.SECONDARY"
                                    iconLeft="printer"
                                    (click)="exportPdf(report.type, report.title)">
                                    {{ 'USER-EDIT-REPORT.GENERATE-REPORT' | translate }}
                                </app-5ways-button>
                            </div>
                        </mat-card>
                    </ng-container>

                    <ng-container *ngIf="report.type === 'gridOneQuestion' || report.type === 'gridQuestion'">
                        <mat-card>
                            <h3>{{ report.title }}</h3>
                            <p> {{ report.totalCounter }}
                                <ng-container *ngIf="report.totalCounter === 1; else pluralResponse">{{ 'POLL-REPORT.ANSWER' | translate }}</ng-container>
                                <ng-template #pluralResponse>{{ 'POLL-REPORT.ANSWERS' | translate }}</ng-template>
                                {{ 'POLL-REPORT.PEOPLE-ANSWERED' | translate }}
                            </p>
                            <ngx-charts-bar-vertical-2d
                                    [results]="report.value"
                                    [yAxis]="true"
                                    [xAxis]="true"
                                    [legend]="showLegend"
                                    [legendPosition]="legendPosition"
                                    [scheme]="colorScheme"
                                    [showXAxisLabel]="true"
                                    [showYAxisLabel]="true"
                                    [yAxisTickFormatting]="yAxisTickFormatting"
                                    [view]="getView()"
                            >
                            </ngx-charts-bar-vertical-2d>
                            <div class="report-buttons">
                                <app-5ways-button
                                    [variant]="ButtonVariant.SECONDARY"
                                    iconLeft="download"
                                    (click)="xlsxExport(report.type, report.title)">
                                    {{ 'POLL-REPORT.EXPORT-XLSX' | translate }}
                                </app-5ways-button>
                                <app-5ways-button
                                    [variant]="ButtonVariant.SECONDARY"
                                    iconLeft="printer"
                                    (click)="exportPdf(report.type, report.title)">
                                    {{ 'USER-EDIT-REPORT.GENERATE-REPORT' | translate }}
                                </app-5ways-button>
                            </div>
                        </mat-card>
                    </ng-container>
                </ng-container>
            </ng-container>
        </ng-container>
    </ng-container>

    <ng-template #noGridOneQuestion>
        <!-- Tutaj możesz umieścić kod dla przypadku, gdy nie ma raportów typu "gridOneQuestion" -->
    </ng-template>

    <ng-template #noData>
        <mat-card>
            {{ 'POLL-REPORT.NO-RESPONDENTS' | translate }}
        </mat-card>
    </ng-template>

    <div *ngIf="activeRespondent.submission_time">
        <p class="poll-report-submission-time">{{ 'POLL-REPORT.SUBMITTED' | translate }} {{activeRespondent.submission_time}}</p>
    </div>
</div>

<ng-template #loader>
    <div class="loader" fxLayout="column" fxLayoutAlign="center center">
        <ng-container *ngIf="totalAnswersPage > 1; else spinner">
            <mat-progress-spinner mode="determinate"
                                  [value]="(currentAnswersPage / totalAnswersPage) * 100"></mat-progress-spinner>
            <div class="loader-percentage">
                {{ ((currentAnswersPage / totalAnswersPage) * 100) | number: '1.0-0' }} %
            </div>
        </ng-container>
        <ng-template #spinner>
            <mat-spinner></mat-spinner>
        </ng-template>
    </div>
</ng-template>
