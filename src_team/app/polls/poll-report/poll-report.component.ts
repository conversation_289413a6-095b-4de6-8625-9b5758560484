import {AfterViewInit, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild} from '@angular/core';
import {PollAnswerService} from '../../services/poll-answer.service';
import {Observable, of, Subscription} from 'rxjs';
import {concatMap, tap} from 'rxjs/operators';
import {AnswerReportInterface} from '../../common/interfaces/answer-report.interface';
import {PollAnswerInterface} from '../../common/interfaces/poll-answer.interface';
import * as _ from 'underscore';
import {animate, state, style, transition, trigger} from '@angular/animations';
import {environment} from '../../../environments/environment';
import { MatSelectChange, MatFormField, MatLabel, MatSelect } from '@angular/material/select';
import * as XLSX from 'xlsx';
import {MatPaginator, MatPaginatorIntl, PageEvent} from '@angular/material/paginator';
import {DomSanitizer} from '@angular/platform-browser';
import {PdfExportService} from '../../services/pdf-export.service';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import {PollReportService} from '../../services/poll-report.service';
import {PollAnswersCountService} from '../../services/poll-answers-count.service';
import {PollResponse} from '../../common/interfaces/poll.interface';
import { NgIf, NgFor, JsonPipe, DecimalPipe } from '@angular/common';
import { MatCard } from '@angular/material/card';
import { MatOption } from '@angular/material/autocomplete';
import { MatInput } from '@angular/material/input';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { PieChartModule, BarChartModule } from '@swimlane/ngx-charts';
import { DefaultLayoutDirective, DefaultLayoutAlignDirective } from 'ngx-flexible-layout/flex';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { ButtonComponent } from '../../elements/button/button.component';
import { ButtonVariant } from '../../common/enums/button-variant.enum';

@Component({
    selector: 'app-poll-report',
    templateUrl: './poll-report.component.html',
    styleUrls: ['./poll-report.component.scss'],
    animations: [
        trigger('fadeInOut', [
            state('void', style({
                opacity: 0
            })),
            transition('void <=> *', animate(300)),
        ])
    ],
    providers: [
        {
            provide: MatPaginatorIntl,
            useClass: class extends MatPaginatorIntl {
                constructor() {
                    super();
                    this.itemsPerPageLabel = '';
                    this.nextPageLabel = 'Następna odpowiedź';
                    this.previousPageLabel = 'Poprzednia odpowiedź';
                    this.getRangeLabel = (page: number, pageSize: number, length: number): string => {
                        if (length === 0 || pageSize === 0) {
                            return `0 z ${length}`;
                        }
                        const currentPage = page + 1;
                        return `${currentPage} z ${length}`;
                    };
                }
            }
        }
    ],
    imports: [NgIf, MatCard, MatFormField, MatLabel, MatSelect, NgFor, MatOption, MatPaginator, MatInput, MatButton, MatIcon, PieChartModule, BarChartModule, DefaultLayoutDirective, DefaultLayoutAlignDirective, MatProgressSpinner, JsonPipe, DecimalPipe, TranslatePipe, ButtonComponent]
})

export class PollReportComponent implements OnInit, AfterViewInit, OnDestroy {

    @Output() exportPdfFn: EventEmitter<void> = new EventEmitter<void>();

    @Output() exportXlsxFn: EventEmitter<void> = new EventEmitter<void>();

    @Input()
    widgetUuid: string;

    @Input()
    questions = []; // obiekt przechowujący strukturę ankiety

    @Input()
    isAnonymous: boolean; // informacja o anonimowości ankiety

    submissionTime: string; // czas, kiedy odpowiedź została przesłana

    respondents: Array<{
        id: number,
        email: string,
        company?: string,
        firstname?: string,
        lastname?: string,
        phone?: string
    }> = [{id: 0, email: 'Wszyscy'}];

    activeRespondent: {
        id: number,
        email?: string,
        company?: string,
        firstname?: string,
        lastname?: string,
        phone?: string
        submission_time?: string
    } = {id: 0}; // obiekt przechowujący dane aktywnego respondenta ankiety

    // ilość stron z odpowiedziami klientów
    totalAnswersPage = 0;

    // numer aktualnej strony z odpowiedziami klientów
    currentAnswersPage = 0;

    // ilość odpowiedzi klientów pobrana z _pollAnswerService
    totalAnswers: number;

    // obiekt agregujący odpowiedzi na pytania z licznikami
    answersReport = [];

    // zliczanie wszystkich liczników per pytanie
    totalCounter: number = 0;

    public showLoader: boolean = true;

    // limit odpowiedzi na jedno zapytanie o odpowiedzi klientów
    private _answerLimitPerRequest = 100;

    wideDesktopView = [800, 400];
    laptopView = [400, 400];
    desktopView = [600, 300];

    colorScheme = {
        domain: [
            '#9D27B0',
            '#4CAF50',
            '#4050B5',
            '#03BCD4',
            '#029688',
            '#FF5722',
            '#FF9800',
            '#F48FB1',
            '#C5CAE9',
            '#FFF59D',
            '#BCAAA4',
            '#EF9A9A'
        ]
    };

    showLegend: boolean = true;
    showLabels: boolean = true;
    legendPosition: string = 'below';
    pollsWithNewAnswers: PollResponse[] = [];

    devMode = environment.devMode;
    pollTitle: string;

    @ViewChild(MatPaginator) paginator: MatPaginator;

    answerDataSub$: Subscription;
    pollType: string;
    ButtonVariant = ButtonVariant;

    constructor(
        private _pollAnswerService: PollAnswerService,
        private _pdfExportService: PdfExportService,
        private _translate: TranslateService,
        private _pollReportService: PollReportService,
        private _pollAnswersCountService: PollAnswersCountService) {
    }

    ngOnInit(): void {
        this.getPollType();
        this.fetchAnswerData().subscribe();
        this.fetchAnswersCount();
        this.showSingleReport();
    }

    ngAfterViewInit() {
        const matchingPoll = this.pollsWithNewAnswers.find(poll => poll.poll_id === this.widgetUuid);

        if (matchingPoll) {
            this.updateAnswersCount(matchingPoll);
        }
    }

    ngOnDestroy() {
        if (this.answerDataSub$) {
            this.answerDataSub$?.unsubscribe();
        }
    }

    updateAnswersCount(matchingPoll: PollResponse) {
        if (matchingPoll) {
            const pollId = matchingPoll.id;
            const data = {
                poll_id: matchingPoll.poll_id,
                seen_answers_count: matchingPoll.total
            };

            this._pollAnswersCountService.updatePollAnswersCount(pollId, data)
                .subscribe(
                    error => {
                        console.error('Błąd przy aktualizacji ankiety:', error);
                    }
                );
        } else {
            console.error('Nie znaleziono ankiety.');
        }
    }

    restartCounters() {
        this.totalAnswersPage = 0;
        this.currentAnswersPage = 0;
        this.totalAnswers = 0;
        this.totalCounter = 0;
    }

    calculatePages() {
        this.totalAnswersPage = Math.ceil(this.totalAnswers / this._answerLimitPerRequest);
    }

    finishLoadData() {
        setTimeout(() => this.showLoader = false, 500);

        return of('finishLoadData');
    }

    addRespondent(respondent) {
        this.respondents.push(respondent);
    }

    onChangeRespondent(event: MatSelectChange) {
        const respondentIndex = this.respondents.findIndex(respondent => respondent.id === event.value);
        this.activeRespondent = this.respondents[respondentIndex];

        this.answerDataSub$ = this.fetchAnswerData(this.activeRespondent.id).subscribe();
    }

    fetchAnswerData(answerId = null): Observable<any> {
        this.showLoader = true;
        this.answersReport = [];
        this.restartCounters();

        return this.fetchAnswerDataRecursive(answerId);
    }

    fetchAnswerDataRecursive(answerId = null, page = 1): Observable<any> {
        let condition = `?poll_id=${this.widgetUuid}&limit=${this._answerLimitPerRequest}&page=${page}`;

        if (answerId) {
            condition += `&id=${answerId}`;
        }

        return this._pollAnswerService.getPollAnswer(condition).pipe(
            tap((response: {results, total: number}) => {
                this.totalAnswers = response.total;

                if (response.total === 0) {
                    return this.finishLoadData();
                }

                if (!this.totalAnswersPage) {
                    this.calculatePages();
                }
            }),
            concatMap((response: {results, total: number}): any => {
                this.processResults(response.results);

                if (page < this.totalAnswersPage) {
                    this.currentAnswersPage++;
                    return this.fetchAnswerDataRecursive(answerId, page + 1);
                } else {
                    return this.finishLoadData();
                }
            })
        );
    }

    processResults(results: any[]) {
        results.forEach(result => {
            const respondentEmail = result.email;
            if (result.email && !this.respondents.find(respondent => +respondent.id === +result.id)) {
                this.addRespondent({
                    id: +result?.id,
                    poll_id: result?.poll_id,
                    email: result?.email,
                    company: result?.company,
                    firstname: result?.firstname,
                    lastname: result?.lastname,
                    phone: result?.phone,
                    submission_time: result?.submission_time
                });
            }

            const parsedResult = JSON.parse(result?.poll_json);

            parsedResult.forEach((answer: PollAnswerInterface) => {
                this.processAnswer(answer, respondentEmail);
            });
        });
    }

    processAnswer(answer: PollAnswerInterface,  email: string) {

        if (answer.data.options) {
            answer.data.options.forEach(option => {
                option.value = new DOMParser().parseFromString(option.value, 'text/html').documentElement.textContent;
            });
        }

        const answerReport: AnswerReportInterface = this.answersReport.find((report: any) => report.title === answer.data.title && report.type === answer.type) || {};
        const question = this.questions.find(q => q.data?.title === answer.data?.title && q.type === answer.type);

        if (answerReport?.value && Array.isArray(answerReport.value)) {
            answerReport.value.forEach((answerValue: { name: string; }) => {
                if (answerValue?.name) {
                    answerValue.name = new DOMParser().parseFromString(answerValue.name, 'text/html').documentElement.textContent;
                }
            });
        }

        if (question && !_.isEmpty(answerReport)) {
            this.updateExistingAnswerReport(answer, answerReport, email);
        } else if (question) {
            this.createNewAnswerReport(answer, question, email);
        }
    }

    updateExistingAnswerReport(answer: PollAnswerInterface, answerReport: AnswerReportInterface, email?: string | null) {
        let haveAnswer = false;

        switch (answer.type) {
            case 'shortQuestion':
            case 'longQuestion':
                if (answer?.data?.value && answer.data?.value.length >= 1) {
                    answerReport.totalCounter += 1;
                    answerReport.value.push(answer?.data?.value);
                }
                break;

            case 'oneOptionQuestion':
            case 'multipleOptionQuestion':
            case 'selectQuestion':
                answer.data.options.forEach(option => {
                    if (option?.value) {
                        const optionQuestion = answerReport.value.find(data => data?.name === option?.value);

                        if (optionQuestion && option?.selected) {
                            optionQuestion['value'] += 1;
                            optionQuestion['emails'] = optionQuestion['emails'] || [];
                            optionQuestion['emails'].push(email);
                            haveAnswer = true;
                        } else if (!optionQuestion && option?.selected) {
                            haveAnswer = true;
                            answerReport.value.push({name: option?.value, value: 1, emails: [email]});
                        }
                    }
                });

                answerReport.totalCounter = haveAnswer ? answerReport.totalCounter + 1 : answerReport.totalCounter;

                break;

            case 'lineScaleQuestion':
                const answerValue = Number(answer.data.value);
                const optionQuestion = answerReport.value.find(data => Number(data?.name) === answerValue);

                if (optionQuestion) {
                    optionQuestion['value'] += 1;
                    optionQuestion['emails'] = optionQuestion['emails'] || [];
                    optionQuestion['emails'].push(email);
                    haveAnswer = true;
                } else if (answerValue >= 0) {
                    answerReport.value.push({ name: answerValue, value: 1, emails: [email] });
                    answerReport.value = _.sortBy(answerReport.value, 'name');
                    haveAnswer = true;
                }

                answerReport.totalCounter = haveAnswer ? answerReport.totalCounter + 1 : answerReport.totalCounter;

                break;

            case 'gridOneQuestion':
            case 'gridQuestion':
                this.processGridQuestion(answer, answerReport, email);

                break;
        }
    }

    createNewAnswerReport(answer: PollAnswerInterface, question: any, email) {
        const answerReport: AnswerReportInterface = {
            totalCounter: 0,
            type: answer?.type,
            title: answer?.data?.title,
            value: []
        };

        switch (answer.type) {
            case 'shortQuestion':
            case 'longQuestion':
                this.initTextQuestions(answer, answerReport);

                if (answer.data?.value && answer.data?.value.length >= 1) {
                    answerReport.totalCounter += 1;
                    answerReport.value.push(answer.data?.value);
                }

                break;

            case 'oneOptionQuestion':
            case 'multipleOptionQuestion':
            case 'selectQuestion':
                this.initOptionQuestions(question, answer, answerReport, email);

                break;

            case 'lineScaleQuestion':
                this.initLineScale(answer, answerReport, email);

                break;

            case 'gridOneQuestion':
            case 'gridQuestion':
                this.initGridQuestion(answer, answerReport, email);

                break;
        }

        this.answersReport.push(answerReport);
    }

    private initTextQuestions(answer: PollAnswerInterface, answerReport: AnswerReportInterface) {
        answerReport.showAnswersCount = 4;
        answerReport.expanded = false;
    }

    private initOptionQuestions(question: any, answer: PollAnswerInterface, answerReport: AnswerReportInterface, email) {
        let haveAnswer = false;

        question.data?.options.forEach(option => {
            answerReport.value.push({name: option.value, value: 0, emails: []});
        });

        answer.data?.options.forEach(option => {
            if (option.selected) {
                const answerReportOption = answerReport.value.find(data => data.name === option.value);

                haveAnswer = true;

                if (answerReportOption) {
                    answerReportOption.value = 1;
                    answerReportOption.emails = [email];
                } else {
                    answerReport.value.push({name: option.value, value: 1, emails: [email]});
                }
            }
        });

        answerReport.totalCounter = haveAnswer ? answerReport.totalCounter + 1 : answerReport.totalCounter;
    }

    initLineScale(answer: PollAnswerInterface, answerReport: AnswerReportInterface, email?: string) {
        let haveAnswer = false;

        answerReport.value = [];

        const labelFrom = +answer.data?.line_scale[0].label_from?.value;
        const labelTo = +answer.data?.line_scale[1].label_to?.value;
        const userAnswerValue = +answer.data?.value;

        const scaleValues = Array.from({ length: labelTo - labelFrom + 1 }, (_, index) => labelFrom + index);

        scaleValues.forEach(i => {
            if (i === userAnswerValue) {
                answerReport.value.push({ name: i.toString(), value: 1, emails: [email] });
                haveAnswer = true;
            }
        });


        if (haveAnswer) {
            answerReport.totalCounter += 1;
        }
    }


    private initGridQuestion(answer: PollAnswerInterface, answerReport: AnswerReportInterface, email: string) {
        let haveAnswer = false;

        answer.data?.rows.forEach(row => {
            const rowData = {name: row.value, series: []};

            answer.data?.columns.forEach((column, columnIndex) => {
                if (row.selected.includes(columnIndex)) {
                    rowData.series.push({name: column, value: 1, emails: [email]});
                    haveAnswer = true;
                }
            });

            if (rowData.series.length > 0) {
                answerReport.value.push(rowData);
            }
        });
        answerReport.totalCounter = haveAnswer ? answerReport.totalCounter + 1 : answerReport.totalCounter;
    }

    private processGridQuestion(answer: PollAnswerInterface, answerReport: AnswerReportInterface, email: string) {
        let haveAnswer = false;

        answer.data?.rows.forEach(row => {
            const rowData = {name: row.value, series: []};

            answer.data?.columns.forEach((column, columnIndex) => {
                if (row.selected.includes(columnIndex)) {
                    rowData.series.push({name: column, value: 1, emails: [email]});
                    haveAnswer = true;
                }
            });

            if (rowData.series.length > 0) {
                answerReport.value.push(rowData);
            }
        });

        const acc = [];

        answerReport.value.forEach((answer) => {
            const accElement = acc.find(data => data.name === answer.name);

            if (accElement) {
                accElement.series = [...accElement.series, ...answer.series];

                const accSeries = [];

                accElement.series.forEach((series) => {
                    const accSeriesElement = accSeries.find(data => data.name === series.name);

                    if (accSeriesElement) {
                        accSeriesElement.value += 1;
                        accSeriesElement.emails.push(...series.emails);
                    } else {
                        accSeries.push({name: series.name, value: series.value, emails: [...series.emails]}); // Przepisanie emaili
                    }
                });
                accElement.series = accSeries;
            } else {
                acc.push(answer);
            }
        });

        answerReport.value = acc;
        answerReport.totalCounter = haveAnswer ? answerReport.totalCounter + 1 : answerReport.totalCounter;
    }

    generateTable(questionType, questionTitle): string {
        let trElements = '';

        this.answersReport.forEach((report) => {
            if (report.type === questionType && report.value && report.title === questionTitle) {
                switch (report.type) {
                    case 'oneOptionQuestion':
                    case 'selectQuestion':
                    case 'multipleOptionQuestion':
                    case 'lineScaleQuestion':
                        trElements += `<tr><th>Odpowiedzi:</th><th>Liczba odpowiedzi:</th></tr>`;
                        report.value.forEach((option) => {
                            trElements += `<tr><td>${this.formatAnswer(option.name)}</td><td>${this.formatAnswer(option.value)}</td></tr>`;
                        });
                        break;

                    case 'gridQuestion':
                    case 'gridOneQuestion':
                        trElements += `<tr><th>Odpowiedzi:</th><th>Liczba odpowiedzi:</th></tr>`;
                        report.value.forEach((row) => {
                            row.series.forEach((col) => {
                                trElements += `<tr><td>${this.formatAnswer(row.name)}: ${this.formatAnswer(col.name)}</td><td>${this.formatAnswer(col.value)}</td></tr>`;
                            });
                        });
                        break;

                    default:
                        trElements += `<tr><th>Odpowiedzi:</th></tr>`;
                        report.value.forEach((answer) => {
                            trElements += `<tr><td>${this.formatAnswer(answer)}</td></tr>`;
                        });
                        break;
                }
            }
        });

        return `<table id="excel-table">${trElements}</table>`;
    }

    xlsxExportAll(): Observable<void> {
        return new Observable<void>((observer) => {
            this.pollTitle = this.questions[0].data.title;

            const tableHtml = this._pollReportService.generateAllTableData(this.questions, this.answersReport, this.pollTitle, this.isAnonymous, this.respondents);
            const element = document.createElement('div');
            element.innerHTML = tableHtml;
            const tableElement = element.querySelector('table');

            const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(tableElement);

            ws['!cols'] = [
                { wch: 100 },
                { wch: 20 }
            ];

            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Arkusz 1');
            XLSX.writeFile(wb, `5ways_${this.pollTitle}.xlsx`);

            observer.next(null);
            observer.complete();
        });
    }

    xlsxExport(questionType, questionTitle) {
        const tableHtml = this.generateTable(questionType, questionTitle);
        const element = document.createElement('div');
        element.innerHTML = tableHtml;
        const tableElement = element.querySelector('table');

        const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(tableElement);

        ws['!cols'] = [
            { wch: 100 },
            { wch: 20 }
        ];

        const wb: XLSX.WorkBook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'Arkusz 1');
        XLSX.writeFile(wb, `5ways_${questionTitle}.xlsx`);
    }

    showMoreAnswers(answerReport) {
        answerReport.showAnswersCount += 4;
        answerReport.expanded = true;
    }

    toggleShowAllAnswers(answerReport) {
        if (!answerReport.showAllAnswers) {
            answerReport.showAnswersCount = answerReport.value.length; // Aktualizuj showAnswersCount przed przełączeniem showAllAnswers
            answerReport.showAnswersCount = 4;
        } else {
            answerReport.showAnswersCount = 4;
        }

        answerReport.showAllAnswers = !answerReport.showAllAnswers;
    }

    yAxisTickFormatting = (value: string) => {
        if (Number.isInteger(+value)) {
            return +value.toString();
        } else {
            return '';
        }
    };

    getView() {
        if (window.innerWidth >= 1900) {
            return this.wideDesktopView;
        } else if (window.innerWidth < 1650) {
            return this.laptopView;
        } else {
            return this.desktopView;
        }
    }

    maxLabelLength() {
        if (window.innerWidth >= 1900) {
            return 35;
        } else if (window.innerWidth > 1650 && window.innerWidth < 1900) {
            return 30;
        } else {
            return 20;
        }
    }

    onPageChange(event: PageEvent) {
        const respondentIndex = event.pageIndex;

        if (respondentIndex >= 0 && respondentIndex < this.respondents.length) {
            this.activeRespondent = this.respondents[respondentIndex];

            if (this.activeRespondent) {
                this.answerDataSub$ = this.fetchAnswerData(this.activeRespondent.id).subscribe();
            }
        }
    }

    getRespondentIndex(): number {
        return this.respondents.findIndex(respondent => respondent.id === this.activeRespondent.id);
    }

    private getPollType() {
        const header = this.questions.find(question => question.type === 'header');
        if (header && header.data && header.data.pollType) {
            this.pollType = header.data.pollType;
        }
    }

    exportPdf(questionType, questionTitle) {
        const watermarkText = this._translate.instant('ISSUE-VIEW-MENU.ISSUE-PRINT');
        const texts = {
            title: questionTitle,
            author: '5 ways...',
            footer: watermarkText,
            fileName: `5ways_${questionTitle}.pdf`
        };

        this._pdfExportService.initPdf(texts);
        this._pdfExportService.pdf.add({
            text: questionTitle,
            style: 'subject'
        });

        this._pdfExportService.pdf.add(this._pdfExportService.pdf.ln(1));

        const respondentInfo = this.activeRespondent.email ? this.activeRespondent.email : 'Wszyscy';
        const includeEmails = respondentInfo === 'Wszyscy';

        this._pdfExportService.pdf.add({
            text: `Odpowiedź udzielona przez: ${respondentInfo}`,
            style: {
                fontSize: 12,
                italics: true
            }
        });

        this._pdfExportService.pdf.add(this._pdfExportService.pdf.ln(1));

        const tableBody = this.generatePdfTableData(questionType, questionTitle, includeEmails, this.respondents, this.isAnonymous);

        if (!tableBody || tableBody.length === 0) {
            return;
        }

        this.addTableToPdf(tableBody);
        this._pdfExportService.download();
        this._pdfExportService.resetPdf();
    }

    generatePdfTableData(questionType: string, questionTitle: string, includeEmails: boolean, respondents: any[], isAnonymous: boolean): any[] {
        const tableData: any[] = [];
        let hasData = false;

        if (['oneOptionQuestion', 'selectQuestion', 'multipleOptionQuestion', 'lineScaleQuestion', 'gridQuestion', 'gridOneQuestion'].includes(questionType)) {
            if (includeEmails && !isAnonymous) {
                tableData.push(['Odpowiedzi', 'Liczba odpowiedzi', 'E-maile respondentów']);
            } else {
                tableData.push(['Odpowiedzi', 'Liczba odpowiedzi']);
            }
        } else if (['shortQuestion', 'longQuestion'].includes(questionType)) {
            if (includeEmails && !isAnonymous) {
                tableData.push(['Odpowiedzi', 'E-maile respondentów']);
            } else {
                tableData.push(['Odpowiedzi']);
            }
        }

        this.answersReport.forEach((report) => {
            if (report.type === questionType && report.title === questionTitle) {
                if (report.value && report.value.length > 0) {
                    switch (report.type) {
                        case 'oneOptionQuestion':
                        case 'selectQuestion':
                        case 'multipleOptionQuestion':
                        case 'lineScaleQuestion':
                            this.processReportValues(report.value, tableData, includeEmails, isAnonymous);
                            hasData = true;
                            break;

                        case 'gridQuestion':
                        case 'gridOneQuestion':
                            report.value.forEach((row) => {
                                this.processReportValues(row.series, tableData, includeEmails, isAnonymous, row.name);
                            });
                            hasData = true;
                            break;

                        default:
                            let respondentIndex = 1;
                            report.value.forEach((answer) => {
                                if (answer && answer.trim() !== '') {
                                    const respondent = respondents[respondentIndex];
                                    const respondentEmail = respondent ? respondent.email : '';
                                    const formattedAnswer = this.formatAnswer(answer || '');

                                    if (formattedAnswer) {
                                        const rowData = (includeEmails && !isAnonymous)
                                            ? [formattedAnswer, respondentEmail]
                                            : [formattedAnswer];
                                        tableData.push(rowData);
                                        hasData = true;
                                    }
                                    respondentIndex++;
                                }
                            });
                            break;
                    }
                }
            }
        });

        if (!hasData) {
            return [];
        }

        return tableData;
    }

    processReportValues(values: any[], tableData: any[], includeEmails: boolean, isAnonymous: boolean, rowPrefix?: string): void {
        values.forEach((option) => {
            if (option && option.name && option.value !== undefined) {
                const emails = (includeEmails && !isAnonymous) ? this._pollReportService.getEmails(option.emails || []) : '';
                const formattedOptionName = rowPrefix ? `${this.formatAnswer(rowPrefix)}: ${this.formatAnswer(option.name)}` : this.formatAnswer(option.name);

                if (this.formatAnswer(option.name) && this.formatAnswer(option.value)) {
                    const rowData = (includeEmails && !isAnonymous)
                        ? [formattedOptionName, this.formatAnswer(option.value), emails]
                        : [formattedOptionName, this.formatAnswer(option.value)];
                    tableData.push(rowData);
                }
            }
        });
    }

    formatAnswer(answer: any): string {
        let formattedAnswer = answer;

        if (typeof answer === 'string') {
            if (answer.includes('-') || answer.includes('/')) {
                formattedAnswer = `'${answer}'`;
            } else if (answer.match(/^\d+,\d+$/)) {
                formattedAnswer = answer.replace(',', '.');
            }
        }

        if (!isNaN(parseFloat(formattedAnswer)) && isFinite(formattedAnswer)) {
            formattedAnswer = parseFloat(formattedAnswer).toString();
        }

        return formattedAnswer || '';
    }

    exportGeneralPdf(): Observable<void> {
        return new Observable<void>((observer) => {
            const watermarkText = this._translate.instant('ISSUE-VIEW-MENU.ISSUE-PRINT');
            const surveyTitle = this.questions[0].data.title;
            const respondentEmail = this.activeRespondent.email ? this.activeRespondent.email : 'Wszyscy';
            const includeEmails = respondentEmail === 'Wszyscy';

            const texts = {
                title: surveyTitle,
                author: respondentEmail,
                footer: watermarkText,
                fileName: `5ways_${surveyTitle}.pdf`
            };

            this._pdfExportService.initPdf(texts);

            this._pdfExportService.pdf.add({
                text: surveyTitle, style: 'subject'
            });

            this._pdfExportService.pdf.add(this._pdfExportService.pdf.ln(1));
            this._pdfExportService.pdf.add({
                text: `Odpowiedzi udzielone przez: ${respondentEmail}`, style: {
                    fontSize: 12,
                    italics: true
                }
            });
            this._pdfExportService.pdf.add(this._pdfExportService.pdf.ln(1));

            this.answersReport.forEach(report => {
                this._pdfExportService.pdf.add({
                    text: report.title,
                    style: 'rodo'
                });
                this._pdfExportService.pdf.add(this._pdfExportService.pdf.ln(1));
                const tableBody = this.generatePdfTableData(report.type, report.title, includeEmails, this.respondents, this.isAnonymous);
                this.addTableToPdf(tableBody);
            });
            this._pdfExportService.download();
            this._pdfExportService.pdf = null;

            observer.next(null);
            observer.complete();
        });
    }

    addTableToPdf(tableBody: any[]) {
        if (Array.isArray(tableBody) && tableBody.length > 0) {
            const validRows = tableBody.filter(row =>
                Array.isArray(row) && row.length === tableBody[0].length && row.every(cell => cell !== undefined && cell !== null)
            );

            if (validRows.length > 0) {
                let columnWidths: string[];
                switch (validRows[0].length) {
                    case 3:
                        columnWidths = ['40%', '30%', '30%'];
                        break;
                    case 2:
                        columnWidths = ['50%', '50%'];
                        break;
                    default:
                        columnWidths = ['100%'];
                        break;
                }

                this._pdfExportService.pdf.add({
                    table: {
                        widths: columnWidths,
                        body: validRows
                    },
                    layout: {
                        fillColor: (rowIndex: number) => rowIndex === 0 ? '#b4b1b1' : rowIndex % 2 !== 0 ? '#F5F5F5' : null,
                        hLineWidth: () => 0.5,
                        vLineWidth: () => 0.5,
                        hLineColor: () => '#CCCCCC',
                        vLineColor: () => '#CCCCCC'
                    }
                });

                this._pdfExportService.pdf.add(this._pdfExportService.pdf.ln(2));
            }
        }
    }

    fetchAnswersCount() {
        this._pollAnswersCountService.currentPollData.subscribe(polls => {
            this.pollsWithNewAnswers = polls;
        });
    }


    showSingleReport() {
        if (this.pollType === 'offer') {
            this.answerDataSub$ = this.fetchAnswerData().pipe(
                tap(() => {
                    this.respondents = this.respondents.filter(respondent => respondent.id !== 0);

                    if (this.respondents.length > 0) {
                        this.activeRespondent = this.respondents[0];
                        this.onChangeRespondent({ value: this.activeRespondent.id } as MatSelectChange);
                    }
                })
            ).subscribe();
        }
    }
}

