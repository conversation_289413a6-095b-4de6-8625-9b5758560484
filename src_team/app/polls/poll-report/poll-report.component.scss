@use '../../../variables' as *;

.poll-report-container {
    overflow-x: hidden;

    .poll-report {
        &-submission-time {
            color: rgba(0, 0, 0, .54);
            display: flex;
            font-size: 12px;
            font-style: italic;
            justify-content: flex-end;
            padding: 0 10px 10px 0;
        }
    }

    .poll-report-title {
     margin: 0;
    }

    .poll-report-text {
      margin: 0 0 10px 0;
    }

    h3 {
        margin-bottom: 10px;
    }

    p {
        color: $hard-dark-grey;
        font-size: 14px;
    }

    .mat-card {
        margin-top: 20px;
        margin-bottom: 20px;
    }

    .mat-card:first-child {
        margin-top: 0;
        margin-bottom: 20px;
    }

    .report-buttons {
        display: flex;
        gap: 12px;
        margin-top: 16px;
        align-items: center;
    }
}

.loader {
    margin: 30px 0;
    overflow: hidden;
    height: calc(100vh - 250px);

    .loader-percentage {
        margin-top: 20px;
        font-weight: 500;
        color: $dark-blue;
    }
}

.respondent-selection {
    display: flex;
    flex-direction: column;

    @media (min-width: 1380px) {
        flex-direction: row;
        justify-content: space-between;
    }

    &__field {
        width: 100%;

        @media (min-width: 1380px) {
          width: 60%;
        }
    }
}

:host ::ng-deep {
    .mat-card {
        box-shadow: none;
    }

    .legend-labels {
        color: $hard-dark-grey;
        background: none !important;
        white-space: normal !important;
    }

    .legend-title-text {
        display: none !important;
    }

    .legend-label {

        &-color {
            border-radius: 50% !important;
        }

        &-text {
            color: $hard-dark-grey !important;
        }
    }

    .gridline-path {
        stroke: $dark-blue !important;
    }

    .ngx-charts-outer {
        margin: 0 auto;
    }

    ngx-charts-pie-chart text, .chart-legend .legend-label-text {

        @media (max-width: 1650px) {
            font-size: 11px;
        }

        & {
            font-size: 12px
        }
    }

    .chart-legend .legend-label-text {
        font-size: 11px;
    }

    .mat-paginator {
        .mat-paginator-page-size {
            display: none;
        }
        .mat-icon-button {
            margin: 0 38px;
        }
        .mat-paginator-range-label {
            margin: 0 -132px;
        }

        .mat-paginator-container {
            @media (max-width: 1380px) {
                justify-content: center;
            }
        }
    }
}

ngx-charts-bar-horizontal {
  position: relative;
  right: 15px;
}
