import {Component, EventEmitter, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, Output} from '@angular/core';
import {AuthService} from '../../services/auth/auth.service';
import {TopMenuService} from '../../services/top-menu.service';
import {ButtonVariant} from '../../common/enums/button-variant.enum';
import {of, Subscription, timer} from 'rxjs';
import {EmailIntegrationService} from '../../services/email-integration.service';
import {MatDialog} from '@angular/material/dialog';
import {SocketMailService} from '../../services/sockets/socket-mail.service';
import {catchError, switchMap, tap} from 'rxjs/operators';
import {EmailService} from '../../services/email.service';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import {Store} from '@ngrx/store';
import {selectMailServerId} from '../../store/mail-server/mail-server.selectors';
import {environment} from '../../../environments/environment';
import {MailsIntegrationModalComponent} from '../../mails/mails-integration-modal/mails-integration-modal.component';
import {AsyncPipe, NgIf} from '@angular/common';
import {ButtonComponent} from '../../elements/button/button.component';
import {RouterLink } from '@angular/router';
import {AlertService} from '../../services/alert.service';
import {AlertType} from '../../common/enums/alert-type.enum';
import {AlertDuration} from '../../common/enums/alert-duration.enum';
import {DialogDetailsComponent} from '../dialog-details/dialog-details.component';

@Component({
    selector: 'app-mails-top-menu-buttons',
    templateUrl: './mails-top-menu-buttons.component.html',
    styleUrls: ['./mails-top-menu-buttons.component.scss'],
    imports: [NgIf, ButtonComponent, RouterLink, AsyncPipe, TranslatePipe, DialogDetailsComponent]
})
export class MailsTopMenuButtonsComponent implements OnInit, OnDestroy {
    @Output()
    isOpenChange = new EventEmitter<boolean>();

    mailServerId: number;
    showMailButtons$ = this.topMenuService.showMailButtons$;
    refreshLock = false;
    userId: number;
    endpointUrl = environment.apiUrl + 'mail_server';
    detailsModel: string;
    isDetailsOpen: boolean = false;

    protected readonly ButtonVariant = ButtonVariant;

    private subscriptions = new Subscription();
    private alertService: AlertService = inject(AlertService);
    private authService: AuthService = inject(AuthService);
    private emailIntegrationService: EmailIntegrationService = inject(EmailIntegrationService);
    private dialog: MatDialog = inject(MatDialog);
    private socketMailService: SocketMailService = inject(SocketMailService);
    private emailService: EmailService = inject(EmailService);
    private translate: TranslateService = inject(TranslateService);
    private store: Store = inject(Store);

    constructor(
        private topMenuService: TopMenuService,
    ) {}

    ngOnInit(): void {
        this.checkRefreshLock();
        this.userId = +this.authService.getUserId();
        this.subscriptions.add(
            this.emailIntegrationService.checkEmailIntegrationStatus().pipe(
                switchMap(() => this.store.select(selectMailServerId)),
                tap(id => this.mailServerId = id),
                catchError(error => {
                    this.handleError(error);
                    return of(null);
                })
            ).subscribe()
        );
    }

    ngOnDestroy(): void {
        this.subscriptions?.unsubscribe();
    }

    openDraftAdd() {
        const delayTime = 300;

        this.isDetailsOpen = false;

        timer(delayTime).subscribe(() => {
            this.detailsModel = 'send-message';
            this.isDetailsOpen = true;
        })
    }

    refreshMessages() {
        this.refreshLock = true;

        this.subscriptions.add(
            this.emailIntegrationService.checkEmailIntegrationStatus().pipe(
                switchMap(() => this.emailIntegrationService.verifyMailServerTokenStatus()),
                tap(isVerified => {
                    if (!isVerified) {
                        this.dialog.open(MailsIntegrationModalComponent, {
                            width: '480px',
                            disableClose: true
                        });
                        throw new Error('Mail server token not verified');
                    }
                }),
                switchMap(() => this.emailIntegrationService.getIntegrationMailsFetchingData(this.userId)),
                switchMap(data => this.emailService.refreshEmails(data[0].id)),
                catchError(error => {
                    this.handleError(error);
                    this.alertService.showAlert(
                        this.translate.instant('MAIL-LIST.LIST-ERROR'),
                        AlertType.ERROR,
                        AlertDuration.MEDIUM
                    );
                    return of(null);
                })
            ).subscribe(() => {
                this.socketMailService.lockRefreshEmails();
                this.alertService.showAlert(
                    this.translate.instant('MAIL-LIST.FETCH-PROGRESS'),
                    AlertType.SUCCESS,
                    AlertDuration.MEDIUM
                );
            })
        );
    }

    private handleError(error: any) {
        console.error('Error occurred:', error);
    }

    private checkRefreshLock() {
        this.socketMailService.lockRefreshEmails$.subscribe(isLock => {
            this.refreshLock = isLock;
        });
    }
}
