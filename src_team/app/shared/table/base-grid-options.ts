import { GridOptions } from 'ag-grid-community';

export const BASE_GRID_OPTIONS: GridOptions = {
    tooltipShowDelay: 0,
    suppressNoRowsOverlay: true,
    suppressHorizontalScroll: true,
    rowHeight: 48,
    headerHeight: 40,
    // zapobiega doklejaniu sufiksów przy sortowaniu
    multiSortKey: undefined,
    suppressMultiSort: true,
    icons: {
        sortAscending: '<i-lucide name="chevron-up" style="color: #1A9267; width: 12px; height: 16px; stroke-width: 3px; display: flex; align-items: center;"></i-lucide>',
        sortDescending: '<i-lucide name="chevron-down" style="color: #1A9267; width: 12px; height: 16px; stroke-width: 3px; display: flex; align-items: center;"></i-lucide>',
        sortUnSort: ''
    },
    animateRows: true
};
