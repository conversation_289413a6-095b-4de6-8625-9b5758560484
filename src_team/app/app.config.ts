import {ApplicationConfig, importProvidersFrom, provideZoneChangeDetection, LOCALE_ID} from '@angular/core';
import { LucideAngularModule, icons } from 'lucide-angular';
import {provideRouter, withViewTransitions} from '@angular/router';

import { routes } from './app.routes';
import {HTTP_INTERCEPTORS, HttpClient, provideHttpClient, withInterceptorsFromDi} from '@angular/common/http';
import {StoreModule} from '@ngrx/store';
import {routerReducer, StoreRouterConnectingModule} from '@ngrx/router-store';
import {EffectsModule} from '@ngrx/effects';
import {UserEffects} from './store/user/user.effects';
import {userReducer} from './store/user/user.reducer';
import {mailServerReducer} from './store/mail-server/mail-server.reducer';
import {mailboxReducer} from './store/mailbox/mailbox.reducer';
import {IssueCountersReducer} from './store/issue-counters/issue-counters.reducer';
import {IssueCountersEffects} from './store/issue-counters/issue-counters.effects';
import {avatarReducer} from './store/avatar/avatar.reducer';
import {dialogDetailsReducer} from './store/dialog-details/dialog-details.reducer';
import {AvatarEffects} from './store/avatar/avatar.effects';
import {StoreDevtoolsModule} from '@ngrx/store-devtools';
import {environment} from '../environments/environment';
import {TranslateLoader, TranslateModule} from '@ngx-translate/core';
import {ServiceWorkerModule} from '@angular/service-worker';
import {CookieService} from 'ngx-cookie-service';
import {APP_NAME} from './common/tokens/app-name-token';
import {AuthInterceptor} from './services/auth/auth.interceptor';
import {MAT_FORM_FIELD_DEFAULT_OPTIONS} from '@angular/material/form-field';
import {StripHtmlPipe} from './shared/pipes/strip-html.pipe';
import {TrimWhitespacesPipe} from './shared/pipes/trim-whitespaces.pipe';
import {httpTranslateLoaderFactory} from '../main';
import {provideAnimationsAsync} from '@angular/platform-browser/animations/async';
import {provideAnimations} from '@angular/platform-browser/animations';
import {DateAdapter, MAT_DATE_FORMATS, MAT_NATIVE_DATE_FORMATS, NativeDateAdapter} from '@angular/material/core';
import {DatePipe} from '@angular/common';
import {TdDialogService} from '@covalent/core/dialogs';
import {MailServerAddressesEffects} from './store/mail-server-addresses/mail-server-addresses.effects';

export const appConfig: ApplicationConfig = {
    providers: [
        provideZoneChangeDetection({ eventCoalescing: true }),
        provideRouter(routes, withViewTransitions()),
        provideHttpClient(withInterceptorsFromDi()),
        provideAnimationsAsync(),
        provideAnimations(),
        {
            provide: DateAdapter,
            useClass: NativeDateAdapter
        },
        {
            provide: LOCALE_ID,
            useValue: 'pl-PL',
        },
        {
            provide: MAT_DATE_FORMATS,
            useValue: MAT_NATIVE_DATE_FORMATS
        },
        DatePipe,
        importProvidersFrom(
            LucideAngularModule.pick(icons),
            StoreModule.forRoot({
            router: routerReducer
        }), EffectsModule.forRoot([UserEffects, AvatarEffects, MailServerAddressesEffects]), StoreModule.forFeature('user', userReducer), StoreModule.forFeature('mailServer', mailServerReducer), StoreModule.forFeature('mailbox', mailboxReducer), StoreModule.forFeature('issueCounters', IssueCountersReducer), EffectsModule.forFeature([IssueCountersEffects]), StoreModule.forFeature('avatar', avatarReducer), StoreModule.forFeature('dialogDetails', dialogDetailsReducer), StoreDevtoolsModule.instrument({
            maxAge: 25,
            logOnly: environment.production,
        }),
        StoreModule.forFeature('mailServerAddresses', dialogDetailsReducer),
        StoreRouterConnectingModule.forRoot(), TranslateModule.forRoot({
            loader: {
                provide: TranslateLoader,
                useFactory: httpTranslateLoaderFactory,
                deps: [HttpClient]
            }
        }), ServiceWorkerModule.register('/respondo-service-worker.js', { enabled: environment.production })),
        CookieService,
        {
            provide: APP_NAME,
            useValue: 'RESPONDO'
        },
        {
            provide: HTTP_INTERCEPTORS,
            useClass: AuthInterceptor,
            multi: true
        },
        {
            provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,
            useValue: { appearance: 'outline' }
        },
        StripHtmlPipe,
        TrimWhitespacesPipe,
        provideHttpClient(withInterceptorsFromDi()),
        TdDialogService
    ]
};
