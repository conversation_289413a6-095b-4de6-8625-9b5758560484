import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { MatDialog } from '@angular/material/dialog';
import { UntypedFormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import {of, Subject } from 'rxjs';
import {map, switchMap, debounceTime, takeUntil, catchError } from 'rxjs/operators';
import {UserReportDialogComponent } from '../user-report-dialog/user-report-dialog.component';
import {UserService } from '../../../services/user.service';
import {XlsExportService } from '../../../services/xls-export.service';
import {DurationPipe } from '../../../shared/pipes/duration.pipe';
import {UserInputStorageService } from '../../../services/user-input-storage.service';
import {ReportService } from '../../../services/report.service';
import {endOfDay, startOfDay } from '../../../common/utils/date-utils';
import * as _ from 'underscore';
import {GroupService } from '../../../services/group.service';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import {environment } from '../../../../environments/environment';
import { UserReport } from '../../../common/interfaces/user-report.interface';
import { MatPaginator } from '@angular/material/paginator';
import { DefaultLayoutDirective, DefaultLayoutAlignDirective } from 'ngx-flexible-layout/flex';
import { DefaultClassDirective, DefaultStyleDirective } from 'ngx-flexible-layout/extended';
import { MatCardActions } from '@angular/material/card';
import { MatFormField, MatSuffix, MatLabel } from '@angular/material/select';
import { MatInput } from '@angular/material/input';
import { MatIcon } from '@angular/material/icon';
import { NgIf } from '@angular/common';
import { UserGroupFilterComponent } from '../../../shared/user-group-filter/user-group-filter.component';
import { UserRoleFilterComponent } from '../../../shared/user-role-filter/user-role-filter.component';
import { UserStatusFilterComponent } from '../../../shared/user-status-filter/user-status-filter.component';
import { MatDatepickerInput, MatDatepickerToggle, MatDatepicker } from '@angular/material/datepicker';
import { MatButton } from '@angular/material/button';
import { PaginatorComponent } from '../../../elements/paginator/paginator.component';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef, GridOptions, CellClickedEvent } from 'ag-grid-community';
import { BASE_GRID_OPTIONS } from '../../../shared/table/base-grid-options';
import { SanitizeColumnIdPipe } from '../../../shared/pipes/sanitize-column-id.pipe';

@Component({
    selector: 'app-user-report',
    templateUrl: './user-report.component.html',
    styleUrls: ['./user-report.component.scss'],
    providers: [DurationPipe, SanitizeColumnIdPipe],
    imports: [DefaultLayoutDirective, DefaultClassDirective, MatCardActions, DefaultLayoutAlignDirective, MatFormField, MatInput, FormsModule, MatIcon, MatSuffix, NgIf, UserGroupFilterComponent, UserRoleFilterComponent, UserStatusFilterComponent, MatLabel, MatDatepickerInput, ReactiveFormsModule, MatDatepickerToggle, MatDatepicker, MatButton, DefaultStyleDirective, PaginatorComponent, MatProgressSpinner, TranslatePipe, AgGridAngular]
})
export class UserReportComponent implements OnInit, OnDestroy {
    rowData: UserReport[] = [];
    mapColumnName = {
        'name': this.translate.instant('REPORTS.WORKER'),
        'issues': this.translate.instant('REPORTS.ISSUE-NUMBER'),
        'sentences': this.translate.instant('REPORTS.NUMBER-POSTS'),
        'approved': this.translate.instant('REPORTS.NUMBER-APPROVED'),
        'rejected': this.translate.instant('REPORTS.NUMBER-REJECT'),
        'corrected': this.translate.instant('REPORTS.NUMBER-CORRECTIONS')
    };

    sortField: string = null;
    sortType: 'asc' | 'desc' = null;

    columnDefs: ColDef[] = [
        {
            field: 'name',
            headerName: this.translate.instant('REPORTS.WORKER'),
            flex: 1.86,
            sortable: true,
            cellRenderer: (params) => {
                const cellElement = document.createElement('a');
                cellElement.classList.add('user-name');
                cellElement.textContent = `${params.data.lastname} ${params.data.firstname}`;
                cellElement.addEventListener('click', () => this.onClickUser(params.data.id, this.internalMode));
                return cellElement;
            },
            comparator: (valueA, valueB, nodeA, nodeB) => {
                const lastnameA = nodeA.data.lastname || '';
                const lastnameB = nodeB.data.lastname || '';
                return lastnameA.localeCompare(lastnameB);
            }
        },
        {
            field: 'issues',
            headerName: this.translate.instant('REPORTS.ISSUE-NUMBER'),
            flex: 1,
            sortable: true,
            comparator: (valueA, valueB) => {
                return Number(valueA) - Number(valueB);
            }
        },
        {
            field: 'sentences',
            headerName: this.translate.instant('REPORTS.NUMBER-POSTS'),
            flex: 1,
            sortable: true,
            comparator: (valueA, valueB) => {
                return Number(valueA) - Number(valueB);
            }
        },
        {
            field: 'approved',
            headerName: this.translate.instant('REPORTS.NUMBER-APPROVED'),
            flex: 1,
            sortable: true,
            comparator: (valueA, valueB) => {
                return Number(valueA) - Number(valueB);
            }
        },
        {
            field: 'rejected',
            headerName: this.translate.instant('REPORTS.NUMBER-REJECT'),
            flex: 1,
            sortable: true,
            comparator: (valueA, valueB) => {
                return Number(valueA) - Number(valueB);
            }
        },
        {
            field: 'corrected',
            headerName: this.translate.instant('REPORTS.NUMBER-CORRECTIONS'),
            flex: 1,
            sortable: true,
            comparator: (valueA, valueB) => {
                return Number(valueA) - Number(valueB);
            }
        }
    ];

    getRowStyle = (params) => {
        return {
            cursor: 'pointer',
            backgroundColor: params.node.selected ? '#F3F7FB' : '#FBFBFB',
            borderBottom: '1px solid #DBDFF7',
        };
    };

    defaultColDef: ColDef = {
        flex: 1,
        sortable: true,
        filter: false, // Wyłączamy filtrowanie, aby uniknąć błędu
        resizable: true,
        cellStyle: {
            padding: '12px 16px',
            fontSize: '14px',
            fontWeight: '400',
            letterSpacing: '0.28px'
        }
    };

    gridOptions: GridOptions = {
        ...BASE_GRID_OPTIONS,
        tooltipShowDelay: 0,
        suppressNoRowsOverlay: true,
        suppressHorizontalScroll: true,
        domLayout: 'autoHeight',
        rowHeight: 48,
        headerHeight: 40,
        getRowStyle: this.getRowStyle,
        defaultColDef: {
            sortable: true,
            resizable: true
        },
        onSortChanged: this.applySort.bind(this),
    };

    dateStart: UntypedFormControl;
    dateEnd: UntypedFormControl;

    groupFilterId: number;
    statusFilterVal = 0;
    roleFilterVal;
    selectedGroupId;
    userNameFilter = '';
    internalMode = environment.internalMode;
    loaded = false;

    @ViewChild(MatPaginator) paginator: MatPaginator;
    pageIndex: number = 0;
    pageSize = 10;
    total: number;
    limit: number;

    page = 1;

    private filterChange$ = new Subject<void>();
    private destroy$ = new Subject<void>();
    private gridApi;

    constructor(
        private dialog: MatDialog,
        private reportService: ReportService,
        private userService: UserService,
        private xlsExport: XlsExportService,
        private duration: DurationPipe,
        private groupService: GroupService,
        private userInputStorageService: UserInputStorageService,
        public translate: TranslateService,
        private cd: ChangeDetectorRef
    ) {
        const currentDate = new Date();
        this.dateStart = new UntypedFormControl(currentDate);
        this.dateEnd = new UntypedFormControl(currentDate);
    }

    ngOnInit() {
        this.loadFilterSettings();
        this.getPaginationLimit();
        this.getPageIndex();

        this.filterChange$
            .pipe(
                debounceTime(300),
                takeUntil(this.destroy$)
            )
            .subscribe(() => {
                this.getReportData(this.page);
            });

        this.getReportData(this.page);
    }

    onGridReady(params) {
        this.gridApi = params.api;

        if (this.sortField && this.sortType) {
            this.isSorting = true;

            try {
                let colId = this.sortField;
                if (this.sortField === 'lastname') {
                    colId = 'name';
                }

                const sortModel = [{
                    colId: colId,
                    sort: this.sortType
                }];

                this.gridApi.applyColumnState({
                    state: sortModel,
                    defaultState: { sort: null }
                });
            } finally {
                this.isSorting = false;
            }
        }
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }

    private get reportUsers$() {
        try {

            if (!this.groupFilterId && !this.userNameFilter) {
                return of(null);
            }

            return this.userService.getUsers({ UserGroup_group_id: this.groupFilterId, userName: this.userNameFilter, limit: 999 }, this.groupFilterId.toString()).pipe(
                map((users: any) => {
                    if (users && Array.isArray(users)) {
                        const userIds = users.map(user => user.id);

                        return userIds;
                    } else if (users && users.results && Array.isArray(users.results)) {
                        const userIds = users.results.map(elem => elem.id);

                        return userIds;
                    }
                    return [];
                }),
                catchError(error => {
                    console.error('Error fetching users:', error);
                    return of([]);
                })
            );
        } catch (error) {
            console.error('Error in reportUsers$ getter:', error);
            return of([]);
        }
    }

    private getReportData$(usersIds: string[] | null, page: number = 1) {
        try {
            const startDateStr = startOfDay(new Date(this.dateStart.value));
            const endDateStr = endOfDay(new Date(this.dateEnd.value));

            let params = new HttpParams()
                .set('Report.date_from', startDateStr)
                .set('Report.date_to', endDateStr)
                .set('user_role_id', this.roleFilterVal ? this.roleFilterVal.toString() : 'gt_0')
                .set('page', page.toString())
                .set('limit', this.pageSize.toString());

            if (usersIds && usersIds.length > 0) {
                params = params.set('id', usersIds.join(','));
            }

            if (usersIds && usersIds.length === 0) {
                params = params.set('id', '');
            }

            if (this.userNameFilter && (!usersIds || usersIds.length === 0)) {
                params = params.set('search', this.userNameFilter);
                params = params.set('search_in', 'firstname,lastname');
                params = params.set('search_type', 'AND');
            }

            if (this.statusFilterVal !== undefined) {
                params = params.set('banned', this.statusFilterVal.toString());
            }

            if (this.sortField && this.sortType) {
                params = params.set('order', `${this.sortField}|${this.sortType}`);
            }

            return this.reportService.getReport(params);
        } catch (error) {
            console.error('Error in getReportData$:', error);
            return of({ reports: [], total: 0 });
        }
    }
    private loadFilterSettings() {
        const filterSettings = JSON.parse(this.userInputStorageService.getValue('settings_reports_filters'));
        if (filterSettings) {
            this.groupFilterId = filterSettings.groupFilterId;
            this.roleFilterVal = filterSettings.roleFilterVal;
            this.dateStart.setValue(filterSettings.dateStart);
            this.dateEnd.setValue(filterSettings.dateEnd);
            this.sortField = filterSettings.sortField || null;
            this.sortType = filterSettings.sortType || null;
        }
    }

    private saveFilterSettings() {
        const filterConditions = {
            groupFilterId: this.groupFilterId,
            roleFilterVal: this.roleFilterVal,
            dateStart: this.dateStart.value,
            dateEnd: this.dateEnd.value,
            sortField: this.sortField,
            sortType: this.sortType
        };
        this.userInputStorageService.setValue('settings_reports_filters', JSON.stringify(filterConditions));
    }


    onDateChange(date, control: 'start' | 'end') {
        const dateStartAfterDateEnd = new Date(this.dateStart.value).getTime() > new Date(this.dateEnd.value).getTime();

        if (dateStartAfterDateEnd) {
            if (control === 'start') {
                this.dateEnd.setValue(date);
            } else {
                this.dateStart.setValue(date);
            }
        }

        this.filterChange$.next();
    }

    onClickUser(userId: number, internalMode: boolean) {
        this.dialog.open(UserReportDialogComponent, {
            width: '870px',
            height: '800px',
            data: { userId, internalMode },
            autoFocus: false,
            panelClass: 'full-width-dialog'
        });
    }

    onGroupChange(groupId: number) {
        try {
            this.groupFilterId = groupId || null;

            if (this.paginator) {
                this.paginator.pageIndex = 0;
            }

            this.pageIndex = 0;
            this.page = 1;
            this.setPageIndex(0);
            this.filterChange$.next();
        } catch (error) {
            console.error('Error in onGroupChange:', error);
        }
    }

    onStatusChange(value) {
        this.statusFilterVal = value;

        if (this.paginator) {
            this.paginator.pageIndex = 0;
        }

        this.pageIndex = 0;
        this.page = 1;
        this.setPageIndex(0);
        this.filterChange$.next();
    }

    onRoleChange(value) {
        this.roleFilterVal = value;

        if (this.paginator) {
            this.paginator.pageIndex = 0;
        }

        this.pageIndex = 0;
        this.page = 1;
        this.setPageIndex(0);
        this.filterChange$.next();
    }

    onUserNameFilterChange() {
        try {
            if (this.paginator) {
                this.paginator.pageIndex = 0;
            }

            this.pageIndex = 0;
            this.page = 1;
            this.setPageIndex(0);
            this.filterChange$.next();
        } catch (error) {
            console.error('Error in onUserNameFilterChange:', error);
        }
    }

    getTotal(fieldName: string) {
        if (!this.rowData || this.rowData.length === 0) {
            return 0;
        }
        let summaryTime = 0;
        this.rowData.map(t => summaryTime += +t[fieldName]);
        return summaryTime;
    }

    exportXls() {
        const summaryRow: any = {
            'name': 'SUMA',
            'issues': 0,
            'sentences': 0,
            'approved': 0,
            'rejected': 0,
            'corrected': 0
        };

        const translateSummaryRow: any = {};
        const columnsToInclude = ['name', 'issues', 'sentences', 'approved', 'rejected', 'corrected'];
        const result = this.rowData.map(object => {
            object['name'] = object.firstname + ' ' + object.lastname;
            const filteredObject = _.pick(object, columnsToInclude),
                translate = {};
            _.mapObject(filteredObject, (value, key) => {
                if (key !== 'name' && +value >= 0) {
                    value = +value;
                    summaryRow[key] += value;
                }
                return translate[this.mapColumnName[key]] = value;
            });
            return translate;
        });
        _.mapObject(summaryRow, (value, key) => {
            return translateSummaryRow[this.mapColumnName[key]] = value;
        });
        result.push(translateSummaryRow);
        const colsWidth = [
            { wch: 25 }, { wch: 10 }, { wch: 10 }, { wch: 17 }, { wch: 15 }, { wch: 15 }
        ];
        if (this.internalMode) {
            colsWidth.push({ wch: 15 }, { wch: 15 });
        }
        const workSheetTitle = this.translate.instant('REPORTS.WORK-REPORT');

        const startDate = this.dateStart.value ? new Date(this.dateStart.value).toLocaleDateString() : '';
        const endDate = this.dateEnd.value ? new Date(this.dateEnd.value).toLocaleDateString() : '';

        let headerRow = this.translate.instant('REPORTS.SUMMARY') + ' ' + startDate + ' - ' + endDate + ' ';

        let selectedGroupName = this.translate.instant('REPORTS.ALL');
        if (this.groupFilterId) {
            this.groupService.getGroupListObs().pipe(
                map((res: any) => res.map(elem => ({ id: +elem.id, name: elem.name })))
            ).subscribe(res => {
                if (!res.length) {
                    return;
                }
                const group = res.find(item => item.id === this.groupFilterId);
                if (group) {
                    selectedGroupName = group.name;
                }
            });
        }
        headerRow += this.translate.instant('REPORTS.GROUP') + ' ' + selectedGroupName;

        if (this.roleFilterVal) {
            headerRow += ' | ' + this.translate.instant('REPORTS.ROLE') + ': ' + this.roleFilterVal;
        }

        if (this.statusFilterVal !== undefined) {
            const statusText = this.statusFilterVal === 1 ?
                this.translate.instant('REPORTS.INACTIVE') :
                this.translate.instant('REPORTS.ACTIVE');
            headerRow += ' | ' + this.translate.instant('REPORTS.STATUS') + ': ' + statusText;
        }

        const filename = this.translate.instant('REPORTS.RAPORT') + '_' +
            startDate.replace(/\//g, '-') + '_' + endDate.replace(/\//g, '-');

        this.xlsExport.exportAsExcelFile(result, filename, colsWidth, workSheetTitle, headerRow);
    }

    resetFilters() {
        const wasSorting = this.isSorting;
        this.isSorting = true;

        try {
            this.userNameFilter = '';
            this.groupFilterId = null;
            this.roleFilterVal = null;
            this.statusFilterVal = 0;

            this.sortField = null;
            this.sortType = null;

            const currentDate = new Date();
            this.dateStart.setValue(currentDate);
            this.dateEnd.setValue(currentDate);

            if (this.paginator) {
                this.paginator.pageIndex = 0;
            }
            this.pageIndex = 0;
            this.page = 1;
            this.setPageIndex(0);

            this.saveFilterSettings();

            if (this.gridApi) {
                this.gridApi.applyColumnState({
                    defaultState: { sort: null }
                });
            }

            this.filterChange$.next();
        } finally {
            if (!wasSorting) {
                this.isSorting = false;
            }
        }
    }

    preventDefault(event) {
        event.preventDefault();
    }

    onPageChange(event, page: number) {
        this.pageIndex = page - 1;
        this.pageSize = event.pageSize;
        this.setPaginationLimit(this.pageSize);
        this.setPageIndex(this.pageIndex);
        this.getReportData(this.pageIndex + 1);
    }

    getReportData(page = 1) {
        try {
            this.loaded = false;
            this.saveFilterSettings();

            const wasSorting = this.isSorting;
            this.isSorting = true;

            if (!this.groupFilterId && !this.userNameFilter) {
                this.getReportData$(null, page).subscribe(
                    response => this.handleReportResponse(response, wasSorting),
                    error => this.handleReportError(error, wasSorting)
                );
                return;
            }

            if (!this.groupFilterId && this.userNameFilter) {
                this.getReportData$([], page).subscribe(
                    response => this.handleReportResponse(response, wasSorting),
                    error => this.handleReportError(error, wasSorting)
                );
                return;
            }

            this.reportUsers$.pipe(
                switchMap(usersIds => {
                    if (usersIds === null) {
                        return this.getReportData$(null, page);
                    }
                    return this.getReportData$(usersIds, page);
                }),
                catchError(error => {
                    console.error('Error in getReportData pipe:', error);
                    return of({ reports: [], total: 0 });
                })
            ).subscribe(
                response => this.handleReportResponse(response, wasSorting),
                error => this.handleReportError(error, wasSorting)
            );

        } catch (error) {
            console.error('Error in getReportData:', error);
            this.handleReportError(error, false);
        }
    }

    private handleReportResponse(response: any, wasSorting: boolean) {
        if (response && response.reports) {
            this.total = response.total;
            this.rowData = response.reports;
        } else {
            this.rowData = [];
            this.total = 0;
        }
        this.loaded = true;
        if (!wasSorting) {
            this.isSorting = false;
        }
    }

    private handleReportError(error: any, wasSorting: boolean) {
        console.error('Error loading report data:', error);
        this.rowData = [];
        this.total = 0;
        this.loaded = true;
        if (!wasSorting) {
            this.isSorting = false;
        }
    }

    clearUserNameFilter() {
        this.userNameFilter = '';
        if (this.paginator) {
            this.paginator.pageIndex = 0;
        }
        this.pageIndex = 0;
        this.page = 1;
        this.setPageIndex(0);
        this.filterChange$.next();
    }

    private getPaginationLimit() {
        this.pageSize = +this.userInputStorageService.getValue('userReport_paginationLimit') || 5;
        this.limit = this.pageSize;
    }

    private setPaginationLimit(limit = 5) {
        this.limit = limit;
        this.pageSize = limit;
        this.userInputStorageService.setValue('userReport_paginationLimit', limit);
    }

    private getPageIndex() {
        this.pageIndex = +this.userInputStorageService.getValue('userReport_pageIndex') || 0;
        this.page = this.pageIndex + 1;
    }

    private setPageIndex(pageIndex = 0) {
        this.userInputStorageService.setValue('userReport_pageIndex', pageIndex);
    }

    private isSorting = false;

    public applySort(event) {
        if (this.isSorting) {
            return;
        }

        this.isSorting = true;

        try {
            const api = event?.api || this?.gridApi;
            if (!api) {
                this.isSorting = false;
                return;
            }

            let sortModel;
            try {
                sortModel = api.getSortModel();
            } catch (e) {
                sortModel = api.getColumnState?.().filter(col => col.sort) || [];
            }

            if (sortModel && sortModel.length > 0) {
                const sortedCol = sortModel[0];

                if (sortedCol.colId === 'name') {
                    this.sortField = 'lastname';
                } else {
                    this.sortField = sortedCol.colId;
                }

                this.sortType = sortedCol.sort;

                this.saveFilterSettings();
            } else {
                this.sortField = null;
                this.sortType = null;
                this.saveFilterSettings();
            }
        } catch (error) {
            console.warn('Error applying sort:', error);
        } finally {
            this.isSorting = false;
            this.cd.detectChanges();
        }
    }
}
