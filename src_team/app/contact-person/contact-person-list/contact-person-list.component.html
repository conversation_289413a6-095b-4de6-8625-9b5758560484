<div class="contact-persons">
    <app-dialog-details
            [isOpen]="isDetailsOpen"
            [data]="detailsData"
            [model]="detailsModel"
            (isOpenChange)="isDetailsOpen = $event"
    >
    </app-dialog-details>

    <div class="toolbar">
        <div class="d-flex">
            <app-5ways-paginator
                    class="mr-10"
                    [pageSize]="limit"
                    [pageIndex]="page-1"
                    [length]="totalContactPersons"
                    (page)="onPageEvent($event)"
                    fxHide.md fxHide.xs fxHide.sm>
            </app-5ways-paginator>
            <app-5ways-search-input
                    [(ngModel)]="contactNameFilter"
                    (searchTriggered)="searchByName()"
                    placeholder="{{ 'CONTACT-PERSONS-LIST.SEARCH' | translate }}">
            </app-5ways-search-input>
        </div>
    </div>

    <div class="table-container">
        <div class="table-wrapper" *ngIf="contactPersons?.length">
            <ag-grid-angular
                    class="ag-theme-alpine custom-padding"
                    [rowData]="contactPersons"
                    [columnDefs]="columnDefs"
                    [defaultColDef]="defaultColDef"
                    [rowSelection]="'multiple'"
                    [getRowStyle]="getRowStyle"
                    [rowHeight]="48"
                    [headerHeight]="40"
                    [gridOptions]="gridOptions"
                    (sortChanged)="applySort($event)"
                    [context]="{ componentParent: this }"
                    [domLayout]="'autoHeight'"
                    [suppressRowHoverHighlight]="true">
            </ag-grid-angular>
        </div>

        <div *ngIf="contactPersons && contactPersons.length === 0" class="no-data">
            {{'CONTACT-PERSONS-LIST.EMPTY'| translate}}
        </div>
    </div>
</div>
