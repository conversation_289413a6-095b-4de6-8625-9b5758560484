import {Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import { ContactPersonService } from '../../services/contact-person.service';
import { ContactPersonInterface } from '../../common/interfaces/contact-person.interface';
import { ColDef, GridOptions } from 'ag-grid-community';
import { BASE_GRID_OPTIONS } from '../../shared/table/base-grid-options';
import { HttpParams } from '@angular/common/http';
import { PageEvent } from '@angular/material/paginator';
import { UserInputStorageService } from '../../services/user-input-storage.service';
import { Subscription, timer } from 'rxjs';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import { getContactPersonColumnDefs } from './contact-person-columns.config';
import { ButtonVariant } from '../../common/enums/button-variant.enum';
import { TopMenuAction } from '../../common/interfaces/top-menu-action.interface';
import { TopMenuService } from '../../services/top-menu.service';
import {DialogDetailsComponent} from '../../shared/dialog-details/dialog-details.component';
import {PaginatorComponent} from '../../elements/paginator/paginator.component';
import {SearchInputComponent} from '../../elements/search-input/search-input.component';
import {AgGridModule} from 'ag-grid-angular';
import {FormsModule} from '@angular/forms';
import {CommonModule} from '@angular/common';

@Component({
    selector: 'app-contact-person-list',
    templateUrl: './contact-person-list.component.html',
    styleUrls: ['./contact-person-list.component.scss'],
    imports: [DialogDetailsComponent, PaginatorComponent, SearchInputComponent, TranslatePipe, AgGridModule, FormsModule, CommonModule]
})
export class ContactPersonListComponent implements OnInit, OnDestroy {

    constructor(
        private contactPersonService: ContactPersonService,
        private userInputStorageService: UserInputStorageService,
        private translate: TranslateService,
        private topMenuService: TopMenuService,
    ) {
    }

    private get requestConditions(): HttpParams {
        let params = new HttpParams();
        params = params.set('?', '');

        if (this.contactNameFilter) {
            params = params.set('search', this.contactNameFilter);
        }

        params = params
            .set('search_in', 'first_name,last_name,phone,email,fax')
            .set('page', this.page.toString())
            .set('limit', this.limit.toString())
            .set('order', `${this.sortField}|${this.sortType}`);

        return params;
    }

    private subscriptions = new Subscription();

    public columnDefs: ColDef[];
    public defaultColDef: ColDef = {
        flex: 1,
        sortable: true,
        sortingOrder: ['asc', 'desc'],
        unSortIcon: false,
        filter: true,
        resizable: true,
        cellStyle: {
            padding: '12px 16px',
            fontSize: '14px',
            fontWeight: '400',
            letterSpacing: '0.28px'
        }
    };
    public gridOptions: GridOptions = {
        ...BASE_GRID_OPTIONS,
        tooltipShowDelay: 0,
        suppressNoRowsOverlay: true,
        suppressHorizontalScroll: true
    };
    public sortField = 'last_name';
    public sortType = 'asc';
    public limit: number = 10;
    public page: number = 1;
    public contactPersons: ContactPersonInterface[] = [];
    public totalContactPersons: number;
    public contactNameFilter = '';
    public isDetailsOpen = false;
    public detailsData: any;
    public detailsModel = 'contact-person';
    public readonly ButtonVariant = ButtonVariant;

    ngOnInit(): void {
        this.setPaginationLimit();
        this.columnDefs = getContactPersonColumnDefs(
            this.translate,
            this.openContactPersonDetails.bind(this),
        );
        this.getList();
        this.catchTopMenuAction();
        this.isDetailsOpen = false;

        this.subscriptions.add(
            this.contactPersonService.personEdit$
                .subscribe((result) => {
                    if (result.isOpen) {
                        const delayTime = 300;

                        this.isDetailsOpen = false,
                            timer(delayTime).subscribe(() => {
                                this.detailsData = result.data;
                                this.detailsModel = 'contact-person-add';
                                this.isDetailsOpen = true;
                            });
                    }
                })
        );

        this.subscriptions.add(
            this.contactPersonService.hasChanged$
                .subscribe(() => {
                    this.getList();
                })
        );
    }

    ngOnDestroy(): void {
        this.subscriptions?.unsubscribe();
        this.isDetailsOpen = false;
    }

    private setPaginationLimit(limit = 10) {
        this.limit = limit;
        this.userInputStorageService.setValue('filters_contacts_paginationLimit', limit);
    }

    public getRowStyle = (params) => {
        return {
            cursor: 'pointer',
            backgroundColor: params.node.selected ? '#F3F7FB' : '#FBFBFB',
            borderBottom: '1px solid #DBDFF7',
        };
    };

    public applySort(event) {
        setTimeout(() => {
            const sortModel = event.api.getSortModel();

            if (sortModel && sortModel.length > 0) {
                this.sortField = sortModel[0].colId;
                this.sortType = sortModel[0].sort;
                this.getList();
            }
        });
    }

    public searchByName() {
        this.getList();
    }

    public onPageEvent(event: PageEvent): void {
        this.page = event.pageIndex + 1;
        this.limit = event.pageSize;
        this.setPaginationLimit(this.limit);
        this.getList();
    }

    public getList() {
        this.contactPersonService.getContactPersons(this.requestConditions).subscribe((contactPersons) => {
            this.contactPersons = contactPersons.items.results;
            this.totalContactPersons = contactPersons.totalCount;
        });
    }

    public openContactPersonDetails(contactPersonDetails) {
        const delayTime = 300;

        this.isDetailsOpen = false,
            timer(delayTime).subscribe(() => {
                this.detailsData = contactPersonDetails.data.ContactPerson;
                this.detailsModel = 'contact-person';
                this.isDetailsOpen = true;
            });
    }

    public addContactPerson() {
        const delayTime = 300;

        this.isDetailsOpen = false,
            timer(delayTime).subscribe(() => {
                this.detailsData = undefined;
                this.detailsModel = 'contact-person-add';
                this.isDetailsOpen = true;
            });
    }

    public catchTopMenuAction(): void {
        this.subscriptions.add(this.topMenuService.topMenuActions$.subscribe((action: TopMenuAction) => {
            switch (action.type) {
                case 'ADD':
                    this.addContactPerson();

                    break;
            }
        }));
    }

}
