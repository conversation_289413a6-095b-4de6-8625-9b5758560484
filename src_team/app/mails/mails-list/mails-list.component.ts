import { ChangeDetectorRef, Component, forwardRef, Input, NgZone, OnDestroy, OnInit } from '@angular/core';
import { LoadingStatus } from '../../common/enums/loading-status.enum';
import { SanitizeColumnIdPipe } from '../../shared/pipes/sanitize-column-id.pipe';
import { BehaviorSubject, Subject, Subscription, timer } from 'rxjs';
import { ClientViewService } from '../../services/client-view.service';
import { SidebarsContainerService } from '../../services/sidebars-container.service';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../../services/auth/auth.service';
import { UserInputStorageService } from '../../services/user-input-storage.service';
import { IsMobileService } from '../../services/is-mobile.service';
import { filter, map, switchMap, takeUntil,tap} from 'rxjs/operators';
import {PageEvent} from '@angular/material/paginator';
import {EmailService} from '../../services/email.service';
import {DataResponseInterface} from '../../common/interfaces/data-response.interface';
import {EmailMessageInterface} from '../../common/interfaces/email-message-interface';
import { ToolbarFilter, MailsListToolbarComponent } from '../mails-list-toolbar/mails-list-toolbar.component';
import { HttpParams } from '@angular/common/http';
import {SocketMailService} from '../../services/sockets/socket-mail.service';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import {ColDef, ColGroupDef, GridOptions, GridReadyEvent} from 'ag-grid-community';
import {MailActions} from '../../interfaces/mail-actions';
import {DurationPipe} from '../../shared/pipes/duration.pipe';
import {getMailColumnDefs} from '../../issues/mail-columns.config';
import {KeyValue} from '../../common/interfaces/key-value.interface';
import {ApplicationSettingsService} from '../../services/application-settings.service';
import {BASE_DEFAULT_COL_DEF} from '../../shared/table/base-default-col-def';
import {BASE_GRID_OPTIONS} from '../../shared/table/base-grid-options';
import {MailColumn, MailType } from '../../common/enums/mail.enum';
import { MailRoutePath } from '../../common/enums/route-paths.enum';
import { IssueActions } from '../../interfaces/issue-actions';
import { IssueInitiatorService } from '../../services/issue-initiator.service';
import { DefaultLayoutDirective, DefaultFlexDirective, DefaultLayoutAlignDirective } from 'ngx-flexible-layout/flex';
import { NgSwitch, NgSwitchCase, NgTemplateOutlet } from '@angular/common';
import { DialogDetailsComponent } from '../../shared/dialog-details/dialog-details.component';
import { PaginatorComponent } from '../../elements/paginator/paginator.component';
import { DefaultShowHideDirective } from 'ngx-flexible-layout/extended';
import { AgGridModule } from 'ag-grid-angular';
import { MatProgressSpinner } from '@angular/material/progress-spinner';

@Component({
    selector: 'app-mails-list',
    templateUrl: './mails-list.component.html',
    styleUrls: ['./mails-list.component.scss'],
    providers: [DurationPipe],
    imports: [DefaultLayoutDirective, NgSwitch, NgSwitchCase, NgTemplateOutlet, DefaultFlexDirective, forwardRef(() => DialogDetailsComponent), PaginatorComponent, DefaultShowHideDirective, MailsListToolbarComponent, AgGridModule, DefaultLayoutAlignDirective, MatProgressSpinner, TranslatePipe]
})

export class MailsListComponent implements OnInit, OnDestroy {

    @Input() modal = false;

    @Input() ownerId;

    @Input() hideActions = false;

    @Input() visibleFilters = ['tag', 'priority'];

    @Input() showStatusLabels = false;

    @Input() disableOpenUser = false;

    @Input() context: string;

    @Input() clientId: string | null = null;

    @Input() clientEmail: string;

    /** Kolekcja kolumn, które mają się wyświetlić w tabeli. */
    @Input() showColumns: MailColumn[];

    page = 1;

    initialPage: number;

    limit = 5;

    sortType = 'asc';

    sortField = 'timestamp';

    status: string;

    listName: string;

    userId: number;

    totalMessage: number;

    messagesList: EmailMessageInterface[] = [];

    mobileSubscription: Subscription;
    setIsReadSubscription: Subscription;

    expandAllMessages = false;

    selectedMessageIndex = -1;

    socketSubscription: Subscription = new Subscription();
    subscriptions: Subscription = new Subscription();

    filters: string = '';

    isClientContext = false; // czy wyświetlamy komponent w zakładce z Klientami

    emailType$: BehaviorSubject<string> = new BehaviorSubject<string>(null);
    loadingStatus$: BehaviorSubject<LoadingStatus> = new BehaviorSubject<LoadingStatus>(LoadingStatus.loaded);

    params: HttpParams;

    @Input() userMessagessMode = false;

    groupFilterId: number;

    columnDefs: ColDef[];

    defaultColDef: ColDef = {
        ...BASE_DEFAULT_COL_DEF
    };
    sideBar: {
        toolPanels: [
            {
                id: 'filters',
                labelDefault: 'Filters',
                labelKey: 'filters',
                iconKey: 'filter',
                toolPanel: 'agFiltersToolPanel',
            }
        ]
    };

    selectedIssueIndex = -1;

    loadingStatus: LoadingStatus = LoadingStatus.loaded;

    isDetailsOpen = false;
    detailsData: any;
    mailData;
    detailsModel: string;
    currentMailView: MailRoutePath;

    private gridApi;
    private debounceTimer: any;
    private isDataFetching = false;
    private pendingFetch = false;
    private destroySubject = new Subject<void>();

    gridOptions: GridOptions = {
        ...BASE_GRID_OPTIONS,
        onSortChanged: this.saveState.bind(this),
        // AG Grid v33 - Use autoHeight for automatic sizing
        domLayout: 'autoHeight',
        // Set a consistent row height
        rowHeight: 48
    };

    constructor(
        private emailService: EmailService,
        private sidebarsContainer: SidebarsContainerService,
        private route: ActivatedRoute,
        private authService: AuthService,
        private userInputStorageService: UserInputStorageService,
        private applicationSettingsService: ApplicationSettingsService,
        public isMobileService: IsMobileService,
        private cd: ChangeDetectorRef,
        private socketMailService: SocketMailService,
        private translate: TranslateService,
        private router: Router,
        private cdr: ChangeDetectorRef,
        private ngZone: NgZone,
        private sanitizeColumnIdPipe: SanitizeColumnIdPipe,
        private issueInitiatorService: IssueInitiatorService,
        private clientViewService: ClientViewService
    ) {
    }

    ngOnInit() {
        if (this.route.snapshot?.routeConfig?.path) {
            this.currentMailView = this.route.snapshot?.routeConfig?.path as MailRoutePath;
        }
        this.params = this.getActualParams();
        this.getPaginationLimit();
        this.setupUserSidebar();
        this.loadFilterSettings(this.listName);
        this.setupStandardMode();
        const routeData = this.route.snapshot.data as MailActions;
        const allowedActions = this.getAllowedActions(routeData);

        this.initShowColumns();

        if (!this.clientId) {
            this.columnDefs = getMailColumnDefs(
                this.translate,
                this.openDetails.bind(this),
                this.mailDetails.bind(this),
                allowedActions,
                this.listName,
                this.showColumns,
                this.openClientDetailsByEmail.bind(this)
            );

            this.userId = +this.authService.getUserId();
            this.setupList();
            this.setupUserSidebar();
        }

        if (this.clientId) {
            this.showColumns = [MailColumn.MAIL_SUBJECT, MailColumn.MAIL_ADDRESS, MailColumn.CREATED];

            this.columnDefs = getMailColumnDefs(
                this.translate,
                this.openDetails.bind(this),
                this.mailDetails.bind(this),
                {answer: true},
                this.listName,
                this.showColumns,
                this.openClientDetailsByEmail.bind(this)
            );

            this.visibleFilters = [];
            this.getPaginationLimit();
            this.fetchClientMessages();
            this.isClientContext = !!this.clientId;
        }

        this.setSocketSubscriptions();

        this.setupClientEditSubscription();
    }

    private get filtersString() {
        const statusFilter = this.status ? 'status=' + this.status + '&' : '';

        this.loadFilterSettings(this.listName);

        return statusFilter + this.filters;
    }

    /**
     * Zainicjalizuj kolekcję kolumn do wyświetlenia w tabeli.
     *
     * Jeżeli input "showColumns" komponentu jest niezdefiniowany,
     * dane dotyczące wyświetlania kolumn pobierane są z routingu.
     */
    private initShowColumns(): void {
        this.showColumns = this.showColumns ?? (this.route.snapshot.data as {showColumns: MailColumn[]}).showColumns;
    }

    openDetails(message) {
        const delayTime = 300;
        message.context = this.context || this.listName;
        const mailReceivedViews = [MailRoutePath.IMPORTANT, MailRoutePath.OTHER];
        const isDifferentMessage = +this.detailsData?.data?.id !== +message?.data?.id;

        if (this.setIsReadSubscription) {
            this.setIsReadSubscription?.unsubscribe();
        }

        if (isDifferentMessage) {
            this.ngZone.run(() => {
                this.isDetailsOpen = false;
                this.cdr.detectChanges();
            });

            timer(delayTime).pipe(
                takeUntil(this.destroySubject)
            ).subscribe(() => {
                this.ngZone.run(() => {
                    this.detailsModel = mailReceivedViews.includes(this.currentMailView)
                        ? MailType.RECEIVED
                        : MailType.SENT;
                    this.detailsData = message;

                    if (mailReceivedViews.includes(this.currentMailView) && !+message?.data?.is_read) {
                        this.setIsReadSubscription = this.emailService.setIsRead(+message?.data?.id).subscribe({
                            error: (err) => {
                                console.error('Error marking message as read', err);
                            }
                        });
                    }

                    this.isDetailsOpen = true;
                    this.cdr.detectChanges();
                });
            });
        } else {
            timer(delayTime).pipe(
                takeUntil(this.destroySubject)
            ).subscribe(() => {
                this.ngZone.run(() => {
                    this.isDetailsOpen = !this.isDetailsOpen;
                    this.cdr.detectChanges();
                });
            });
        }
    }

    public onDialogOpenChange(isOpen: boolean): void {
        this.isDetailsOpen = isOpen;

        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }

        this.debounceTimer = setTimeout(() => {
            this.scheduleFetch();
        }, 300);
    }

    getRowStyle = () => {
        return {
            'cursor': 'pointer',
            'backgroundColor': '#FBFBFB'
        };
    };

    mailDetails(event) {
        this.mailData = event.data;
    }

    public applySort(sort) {
        setTimeout(() => {
            try {
                // Get column state using the optimal method for AG Grid v33
                const colState = sort.api.getColumnState();

                // Filter to only include columns that have sort information
                const sortState = colState
                    .filter(s => s.sort != null)
                    .map(s => ({ colId: s.colId, sort: s.sort, sortIndex: s.sortIndex }));

                if (sortState && sortState.length > 0) {
                    // For emails, we always sort by timestamp regardless of which column was clicked
                    this.sortField = this.sanitizeColumnIdPipe.transform(sortState[0].colId);
                    this.sortType = sortState[0].sort;
                    this.saveFilterSettings(this.listName);


                    this.params = this.getActualParams();
                }

                if (this.debounceTimer) {
                    clearTimeout(this.debounceTimer);
                }

                this.debounceTimer = setTimeout(() => {
                    this.scheduleFetch();
                }, 300);

                this.cd.detectChanges();
            } catch (error) {
                console.warn('Error applying sort:', error);
            }
        });
    }

    private saveFilterSettings(listName: string) {
        if (!listName) {
            return;
        }

        const filtersSettings = Object.keys(this.filters).filter(item => this.filters[item].savable).map((item: string) => ({key: item, value: this.filters[item].value})),
            settings = {
                filters: filtersSettings,
                groupFilterId: this.groupFilterId,
                sortField: this.sortField,
                sortType: this.sortType
            };

        this.userInputStorageService.setValue('filters_' + listName, JSON.stringify(settings));
    }

    updateSelectedIssueIndex(event) {
        switch (event.key) {
            case 'ArrowUp':
                if (this.selectedIssueIndex > -1) {
                    this.selectedIssueIndex--;
                }

                break;
            case 'ArrowDown':
                if (this.selectedIssueIndex < this.messagesList.length) {
                    this.selectedIssueIndex++;
                }

                break;
        }
    }

    private setupStandardMode() {
        this.status = this.route.snapshot.data.status;
        this.listName = this.route.snapshot.data.name;
        this.setListSettings();
    }

    ngOnDestroy() {
        this.destroySubject?.next();
        this.destroySubject?.complete();
        this.mobileSubscription?.unsubscribe();
        this.socketSubscription?.unsubscribe();
        this.setIsReadSubscription?.unsubscribe();
        this.subscriptions?.unsubscribe();

        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
    }

    /**
     * Metoda do planowania pobrania danych z uwzględnieniem kolejkowania.
     * Jeśli żądanie jest już w trakcie, oznaczamy że potrzebne jest kolejne odświeżenie.
     */
    private scheduleFetch(): void {
        if (this.isDataFetching) {
            this.pendingFetch = true;
        } else {
            this.isClientContext ?
                this.fetchClientMessages() :
                this.fetchMessages(this.listName);
        }
    }

    /**
     * Sprawdza, czy po zakończeniu bieżącego żądania powinno zostać wykonane kolejne.
     * Wywoływana po zakończeniu każdego żądania HTTP.
     */
    private handleFetchCompletion(): void {
        this.isDataFetching = false;

        if (this.pendingFetch) {
            this.pendingFetch = false;

            setTimeout(() => {
                this.isClientContext ?
                    this.fetchClientMessages() :
                    this.fetchMessages(this.listName);
            }, 0);
        }
    }

    private setSocketSubscriptions(): void {
        switch (this.emailType$.value) {
            case 'important':
            case 'other':
                this.socketSubscription.add(
                    this.socketMailService.receivedMail$.subscribe(mail => {

                        if (this.debounceTimer) {
                            clearTimeout(this.debounceTimer);
                        }

                        this.debounceTimer = setTimeout(() => {
                            this.scheduleFetch();
                        }, 500);
                    })
                );
                break;
            case 'draft':
            case 'sent':
                this.socketSubscription.add(
                    this.socketMailService.sentMail$.subscribe(mail => {
                        if (this.debounceTimer) {
                            clearTimeout(this.debounceTimer);
                        }

                        this.debounceTimer = setTimeout(() => {
                            this.scheduleFetch();
                        }, 500);
                    })
                );
                break;
        }
    }

    private getPaginationLimit() {
        this.limit = +this.userInputStorageService.getValue('mailList_paginationLimit') || 10;
    }

    private setPaginationLimit(limit = 10) {
        this.limit = limit;
        this.userInputStorageService.setValue('mailList_paginationLimit', limit);
    }

    private setupUserSidebar() {
        if (this.modal) {
            return;
        }

        const userSidebar = this.sidebarsContainer.sidebar('users'),
            userSidebarExpand = +this.userInputStorageService.getValue('mailList_userSidebarExpand');

        userSidebar.drawerPush(true);

        this.mobileSubscription = this.isMobileService.isMobileView.subscribe(isMobile => {
            if (isMobile) {
                userSidebar.compress();
            } else {
                userSidebarExpand ? userSidebar.expand() : userSidebar.compress();
            }
        });
    }

    private loadFilterSettings(listName: string) {
        if (!listName) {
            return;
        }

        const settings = JSON.parse(this.userInputStorageService.getValue('filters_' + listName));

        if (!settings) {
            return;
        }

        if (settings.filters) {
            settings.filters.forEach((item: KeyValue) => {
                if (this.filters[item.key]) {
                    this.filters[item.key].value = item.value;
                }
            });
        }

        const otherSettings = Object.keys(settings).filter((key: string) => key !== 'filters');

        if (otherSettings) {
            otherSettings.forEach((key: string) => {
                this[key] = settings[key];
            });
        }
    }

    private setListSettings() {
        const listName = this.listName || this.emailType$.value;
        this.visibleFilters = ['tag', 'priority'];

        switch (listName) {
            case 'sent':
                this.ownerId = this.userId;
                this.sortField = 'timestamp';
                this.sortType = 'desc';

                break;
            case 'draft':
                this.ownerId = this.userId;
                this.sortField = 'timestamp';
                this.sortType = 'desc';

                break;
            case 'important':
                this.ownerId = this.userId;
                this.sortField = 'timestamp';
                this.sortType = 'desc';
                this.visibleFilters = ['tag', 'priority', 'receiver'];

                break;
            default:
                this.ownerId = this.userId;
                this.sortField = 'timestamp';
                this.sortType = 'desc';

                break;
        }
    }

    private fetchMessages(type: string) {
        this.isDataFetching = true;
        this.loadingStatus$.next(LoadingStatus.loading);

        switch (type) {
            case 'important':
                this.emailService.getImportantEmails(this.params, this.filtersString)
                    .pipe(
                        map(response => {
                            return {
                                ...response,
                                results: response.results.map(item => {
                                    return item.MailMessagesReceivedImportant;
                                })
                            };
                        }),
                        tap((result: DataResponseInterface) => {
                            this.totalMessage = result.total;
                            if (result.results.length) {
                                this.messagesList = result.results;
                                this.loadingStatus$.next(LoadingStatus.loaded);
                            } else {
                                this.showNoData();
                            }
                        })
                    )
                    .subscribe({
                        next: () => {
                            this.handleFetchCompletion();
                        },
                        error: () => {
                            this.loadingStatus$.next(LoadingStatus.error);
                            this.handleFetchCompletion();
                        }
                    });
                break;
            case 'other':
                this.emailService.getOtherEmails(this.params, this.filtersString)
                    .pipe(
                        map(response => {
                            return {
                                ...response,
                                results: response.results.map(item => {
                                    return item.MailMessagesReceivedOther;
                                })
                            };
                        }),
                        tap((result: DataResponseInterface) => {
                            this.totalMessage = result.total;
                            if (result.results.length) {
                                this.messagesList = result.results;
                                this.loadingStatus$.next(LoadingStatus.loaded);
                            } else {
                                this.showNoData();
                            }
                        })
                    )
                    .subscribe({
                        next: () => {
                            this.handleFetchCompletion();
                        },
                        error: () => {
                            this.loadingStatus$.next(LoadingStatus.error);
                            this.handleFetchCompletion();
                        }
                    });
                break;
            case 'sent':
                this.emailService.getSentEmails(this.params, this.filtersString)
                    .pipe(
                        map(response => {
                            return {
                                ...response,
                                results: response.results.map(item => {
                                    return item.MailMessagesSentAll;
                                })
                            };
                        }),
                        tap((result: DataResponseInterface) => {
                            this.totalMessage = result.total;
                            if (result.results.length) {
                                this.messagesList = result.results;
                                this.loadingStatus$.next(LoadingStatus.loaded);
                            } else {
                                this.showNoData();
                            }
                        })
                    )
                    .subscribe({
                        next: () => {
                            this.handleFetchCompletion();
                        },
                        error: () => {
                            this.loadingStatus$.next(LoadingStatus.error);
                            this.handleFetchCompletion();
                        }
                    });
                break;
            case 'draft':
                this.emailService.getDrafts(this.params, this.filtersString)
                    .pipe(
                        map(response => {
                            return {
                                ...response,
                                results: response.results.map(item => {
                                    return item.MailSentMysqlMessage;
                                })
                            };
                        }),
                        tap((result: DataResponseInterface) => {
                            this.totalMessage = result.total;
                            if (result.results.length) {
                                this.messagesList = result.results;
                                this.loadingStatus$.next(LoadingStatus.loaded);
                            } else {
                                this.showNoData();
                            }
                        })
                    )
                    .subscribe({
                        next: () => {
                            this.handleFetchCompletion();
                        },
                        error: () => {
                            this.loadingStatus$.next(LoadingStatus.error);
                            this.handleFetchCompletion();
                        }
                    });
        }
    }

    private fetchClientMessages() {
        this.isDataFetching = true;
        const params = new HttpParams().set('client_id', this.clientId);

        this.emailService.getImportantEmails(params, this.filtersString)
            .pipe(
                map(response => {
                    return {
                        ...response,
                        results: response.results.map(item => {
                            return item.MailMessagesReceivedImportant;
                        })
                    };
                }),
                tap((result: DataResponseInterface) => {
                    this.totalMessage = result.total;
                    if (result.results.length) {
                        this.messagesList = result.results;
                        this.loadingStatus$.next(LoadingStatus.loaded);
                    } else {
                        this.showNoData();
                    }
                })
            )
            .subscribe({
                next: () => {
                    this.handleFetchCompletion();
                },
                error: () => {
                    this.loadingStatus$.next(LoadingStatus.error);
                    this.handleFetchCompletion();
                }
            });
    }

    getMessage(message: EmailMessageInterface | any[]): EmailMessageInterface {
        switch (this.emailType$.value) {
            case 'important':
                return message['MailMessagesReceivedImportant'];
            case 'other':
                return message['MailMessagesReceivedOther'];
            case 'sent':
                return message['MailMessagesSentAll'];
            case 'draft':
                return message['MailSentMysqlMessage'];
            default:
                break;
        }
    }

    private showNoData() {
        this.messagesList = [];
        this.totalMessage = 0;
        this.loadingStatus$.next(LoadingStatus.nodata);
        this.cd.detectChanges();
    }

    setupList() {
        this.listName = this.route.snapshot.data.name;
        this.emailType$.next(this.listName);
    }

    onPageEvent(event: PageEvent) {
        this.page = ++event.pageIndex;
        this.setPaginationLimit(event.pageSize);
        this.params = this.getActualParams();

        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }

        this.debounceTimer = setTimeout(() => {
            this.scheduleFetch();
        }, 300);
    }

    onFiltersChanged(filters: ToolbarFilter) {
        switch (filters.type) {
            case 'QUERY':
                if (this.initialPage) {
                    this.page = this.initialPage;
                    this.initialPage = null;
                } else {
                    this.page = 1;
                }

                this.filters = filters.data;

                if (filters.showLoader) {
                    this.loadingStatus$.next(LoadingStatus.loading);
                }

                this.params = this.getActualParams();

                if (this.debounceTimer) {
                    clearTimeout(this.debounceTimer);
                }

                this.debounceTimer = setTimeout(() => {
                    this.scheduleFetch();
                }, 300);
                break;

            case '1_MAIL':
                this.emailService.getEmailById(+filters.data).subscribe(
                    response => response ? this.router.navigateByUrl('/mail/' + response.Issue.id + '?list=' + this.listName) : this.showNoData(),
                    () => this.loadingStatus$.next(LoadingStatus.error)
                );
                break;

            case 'EMPTY':
                this.showNoData();
                break;
        }
    }

    private getActualParams() {
        return new HttpParams()
            .set('limit', this.limit.toString())
            .set('page', this.page.toString())
            .set('order', `${this.sortField}|${this.sortType}`);
    }

    /**
     * Handler zdarzenia gridReady, wywoływany gdy siatka (grid) zostanie w pełni zainicjalizowana.
     * @param params - Obiekt parametrów zdarzenia, zawierający:
     *  - api: Główne API gridu, umożliwiające dostęp do metod operujących na gridzie.
     */
    onGridReady(params: GridReadyEvent) {
        this.gridApi = params.api;
        this.restoreState();
    }

    /**
     * Zapisuje bieżący stan sortowania gridu.
     *
     * Metoda pobiera aktualny model sortowania z API ag-Grid,
     * a następnie zapisuje go w formacie JSON przy użyciu serwisu ApplicationSettingsService pod kluczem 'mailList_agGridState'.
     * Dodatkowo sanityzuje identyfikatory kolumn, usuwając ewentualne przyrostki numeryczne (np. "_1", "_2").
     */
    saveState() {
        try {
            // Get column state using the optimal method for AG Grid v33
            const columnDefs = this.gridApi.getColumnDefs();

            // Filter to only include columns that have sort information and extract only needed properties
            const sanitizedSortState = columnDefs.map((item: ColDef | ColGroupDef) => {
                if ('colId' in item && item.colId) {
                    return {
                        ...item,
                        colId: this.sanitizeColumnIdPipe.transform(item.colId)
                    };
                }
                return item;
            });

            const gridState = { sortState: sanitizedSortState };
            this.applicationSettingsService.setValue('mailList_agGridState', JSON.stringify(gridState));
        } catch (error) {
            console.warn('Error saving sort state:', error);
        }
    }

    /**
     * Przywraca stan sortowania gridu z pamięci.
     *
     * Metoda odczytuje zapisany stan gridu (sortowanie) z serwisu UserInputStorageService,
     * korzystając z klucza 'mailList_agGridState'. Jeśli zapisany stan istnieje, metoda ustawia model sortowania
     * w ag-Grid.
     */
    restoreState() {
        const storageKey = 'mailList_agGridState';
        const agGridState = this.userInputStorageService.getValue(storageKey);

        if (agGridState && this.gridApi) {
            try {
                const { sortState } = JSON.parse(agGridState);
                if (sortState && sortState.length > 0) {
                    // Apply column state using the optimal method for AG Grid v33
                    this.gridApi.applyColumnState({
                        state: sortState,
                        defaultState: { sort: null }
                    });
                }
            } catch (error) {
                console.warn('Failed to restore grid state:', error);
            }
        }
    }

    /**
     * Przetwarza dane akcji (MailActions) i generuje akcje w tabeli,
     * ustawiając flagi dla dostępnych akcji:
     *
     * @param actions - Obiekt IssueActions (opcjonalne flagi: answer).
     * @returns Obiekt allowedActions z flagą: answer.
     */
    private getAllowedActions(actions: MailActions): MailActions {
        return {
            answer: !!actions.answer,
            priority: !!actions.priority,
            isRead: !!actions.isRead,
            forward: !!actions.forward
        };
    }

    private setupClientEditSubscription(): void {
        this.subscriptions.add(
            this.clientViewService.clientEdit$.subscribe((result) => {
                if (result.isOpen) {
                    const delayTime = 300;

                    this.isDetailsOpen = false;
                    timer(delayTime).subscribe(() => {
                        this.detailsData = result.data;
                        this.detailsModel = 'client-add';
                        this.isDetailsOpen = true;
                    });
                }
            })
        );
    }

    /**
     * Otwiera okno szczegółów klienta po kliknięciu na adres e-mail nadawcy w tabeli wiadomości
     * @param mailData - Dane wiadomości zawierające client_id
     */
    openClientDetailsByEmail(mailData: EmailMessageInterface): void {
        if (!mailData || !mailData.client_id || +mailData.client_id <= 0) {
            return;
        }

        const delayTime = 300;
        const clientId = +mailData.client_id;

        if (+this.detailsData?.id !== clientId) {
            this.isDetailsOpen = false;

            this.issueInitiatorService.getIssueInitiator(clientId).pipe(
                filter(clientData => !!clientData && !!clientData.IssueInitiator),
                switchMap(clientData => {
                    return timer(delayTime).pipe(
                        map(() => clientData.IssueInitiator)
                    );
                })
            ).subscribe(clientInitiator => {
                this.detailsData = clientInitiator;
                this.detailsModel = 'clients';
                this.isDetailsOpen = true;
            });
        } else {
            this.isDetailsOpen = !this.isDetailsOpen;
        }
    }
}
