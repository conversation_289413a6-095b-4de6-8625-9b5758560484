<div class="issue-list-component" fxLayout="column" [class.user-mode]="userMessagessMode">
    <ng-container [ngSwitch]="userMessagessMode">
        <ng-container *ngSwitchCase="false" [ngTemplateOutlet]="standardModeToolbar"></ng-container>
    </ng-container>

    <div class="issues-list" [fxFlex]="!hideActions" (keyup)="updateSelectedIssueIndex($event)">
        <ng-container [ngSwitch]="loadingStatus">
            <ng-container *ngSwitchCase="'loaded'" [ngTemplateOutlet]="messagesListTemplate"></ng-container>
            <ng-container *ngSwitchCase="'loading'" [ngTemplateOutlet]="loadingTemplate"></ng-container>
            <ng-container *ngSwitchCase="'error'" [ngTemplateOutlet]="errorTemplate"></ng-container>
            <ng-container *ngSwitchCase="'nodata'" [ngTemplateOutlet]="noDataTemplate"></ng-container>
        </ng-container>
    </div>

    <app-dialog-details [isOpen]="isDetailsOpen" [data]="detailsData" [model]="detailsModel" (isOpenChange)="onDialogOpenChange($event)"></app-dialog-details>
</div>

<ng-template #standardModeToolbar>
    <div class="toolbar">
        <app-5ways-paginator [pageSize]="limit" [pageIndex]="page-1" [length]="totalMessage" (page)="onPageEvent($event)" fxHide.md fxHide.xs fxHide.sm></app-5ways-paginator>
        <app-mails-list-toolbar
                [initialOwnerId]="ownerId"
                [listName]="listName"
                [visibleFilters]="visibleFilters"
                [initialSortField]=sortField
                [initialSortType]=sortType
                (filtersChanged)=onFiltersChanged($event)
        ></app-mails-list-toolbar>
    </div>
</ng-template>

<ng-template #messagesListTemplate>
    <ag-grid-angular class="ag-theme-alpine custom-padding"
         (gridReady)="onGridReady($event)"
         [rowData]="messagesList"
         [columnDefs]="columnDefs"
         [defaultColDef]="defaultColDef"
         [getRowStyle]="getRowStyle"
         (filterChanged)="onFiltersChanged($event)"
         (sortChanged)="applySort($event)"
         [suppressMenuHide]="false"
         [gridOptions]="gridOptions"
         [sideBar]="sideBar"
         [suppressRowHoverHighlight]="true">
    </ag-grid-angular>
</ng-template>

<ng-template #noDataTemplate>
    <div class="panel-info" fxLayoutAlign="center center">
        {{'MAIL-LIST.CLEAR-LIST' | translate}}
    </div>
</ng-template>

<ng-template #loadingTemplate>
    <div fxLayoutAlign="center center" class="full-height">
        <mat-spinner></mat-spinner>
    </div>
</ng-template>

<ng-template #errorTemplate>
    <div class="panel-info" fxLayoutAlign="center center">
        {{'MAIL-LIST.LIST-ERROR' | translate}}
    </div>
</ng-template>
