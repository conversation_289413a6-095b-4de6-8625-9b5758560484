import {Component, ElementRef, EventEmitter, inject, Input, OnDestroy, OnInit, Output, ViewChild} from '@angular/core';
import {Observable, Subject, Subscription} from 'rxjs';
import {MatDialog} from '@angular/material/dialog';
import {TranslatePipe, TranslateService} from '@ngx-translate/core';
import Quill from 'quill';
import {IssueService} from '../../services/issue.service';
import {SidebarsContainerService} from '../../services/sidebars-container.service';
import {IsMobileService} from '../../services/is-mobile.service';
import {EmailMessageInterface} from '../../common/interfaces/email-message-interface';
import {EmailService} from '../../services/email.service';
import {QueueStatus} from '../../common/enums/email-integration.enum';
import {debounceTime, distinctUntilChanged, first, map, startWith, tap} from 'rxjs/operators';
import {MailsIntegrationModalComponent} from '../mails-integration-modal/mails-integration-modal.component';
import {EmailIntegrationService} from '../../services/email-integration.service';
import {DialogService} from '../../services/dialog-details.service';
import {SocketMailService} from '../../services/sockets/socket-mail.service';
import {ActivatedRoute} from '@angular/router';
import dayjs from 'dayjs';
import {COMMA, ENTER, SPACE, TAB} from '@angular/cdk/keycodes';
import {FormControl, ReactiveFormsModule} from '@angular/forms';
import {MatAutocomplete, MatAutocompleteSelectedEvent, MatAutocompleteTrigger, MatOption} from '@angular/material/autocomplete';
import {MatChipGrid, MatChipInput, MatChipInputEvent, MatChipRemove, MatChipRow} from '@angular/material/chips';
import {ClientsService} from '../../services/clients.service';
import {MailType} from '../../common/enums/mail.enum';
import {MailRoutePath} from '../../common/enums/route-paths.enum';
import {AsyncPipe, NgClass, NgForOf, NgIf} from '@angular/common';
import {DefaultClassDirective} from 'ngx-flexible-layout/extended';
import {DefaultFlexDirective, DefaultLayoutAlignDirective, DefaultLayoutDirective, FlexFillDirective} from 'ngx-flexible-layout/flex';
import {QuillEditorComponent} from 'ngx-quill';
import {FormsModule} from '@angular/forms';
import {TemplateFileUploaderComponent} from '../../shared/template-file-uploader/template-file-uploader.component';
import {MatIcon} from '@angular/material/icon';
import {MatButton} from '@angular/material/button';
import {AlertService} from '../../services/alert.service';
import {AlertType} from '../../common/enums/alert-type.enum';
import {AlertDuration} from '../../common/enums/alert-duration.enum';
import {LucideAngularModule} from 'lucide-angular';
import {MatFormField, MatLabel} from '@angular/material/select';
import {MatInput} from '@angular/material/input';

Quill.import('parchment');

@Component({
    selector: 'app-mails-editor',
    templateUrl: './mails-editor.component.html',
    styleUrls: [
        '../../issues/issue-editor/issue-editor.component.scss',
        './mails-editor.component.scss'
    ],
    imports: [
        NgClass,
        DefaultClassDirective,
        DefaultLayoutDirective,
        DefaultLayoutAlignDirective,
        FlexFillDirective,
        DefaultFlexDirective,
        QuillEditorComponent,
        FormsModule,
        NgIf,
        TemplateFileUploaderComponent,
        MatIcon,
        MatButton,
        AsyncPipe,
        TranslatePipe,
        LucideAngularModule,
        ReactiveFormsModule,
        MatAutocomplete,
        MatFormField,
        MatLabel,
        MatChipInput,
        MatAutocompleteTrigger,
        MatOption,
        MatInput,
        NgForOf,
        MatChipGrid,
        MatChipRow,
        MatChipRemove
    ]
})
export class MailsEditorComponent implements OnInit, OnDestroy {
    @Input()
    mailData: EmailMessageInterface;

    @Input()
    isForwarded = false;

    @Output()
    draftChange = new EventEmitter<any>();

    @ViewChild('emailInput')
    emailInput: ElementRef<HTMLInputElement>;

    updatedMailData;
    Quill: any;
    expandedEditor$ = this.issueService.expandedEditor;
    expandedFullEditor$ = this.issueService.expandedFullEditor;
    mailServerId: number;
    isMobile$: Observable<boolean>;
    draftId: number = null;
    editorModules = {
        toolbar: {
            container: [
                ['bold', 'italic', 'underline', 'strike'],
                [{'color': []}, {'background': []}],
                [{'list': 'ordered'}, {'list': 'bullet'}],
                [{'align': []}],
                ['link', 'image'],
                ['clean']
            ]
        },
        clipboard: {
            matchVisual: false
        }
    };

    editorFormats: [
        'bold', 'italic', 'underline', 'strike',
        'color', 'background',
        'list', 'bullet',
        'align',
        'link', 'image'
    ];
    separatorKeysCodes: number[] = [ENTER, COMMA, SPACE, TAB];
    emailCtrl = new FormControl();
    emails: string[] = [];
    bodyFooter: string = '';
    clientsEmails: string[] = [];
    filteredEmails: Observable<string[]>;
    isSendButtonDisabled: boolean = false;
    readonly QueueStatus = QueueStatus;
    private contentChangeSubject = new Subject<any>();
    private subscriptions = new Subscription();
    private editorInitialized = false;
    private initialContent: string = '';
    private draftUpdateTimeout: any;
    private readonly DEBOUNCE_TIME = 2000;

    private alertService: AlertService = inject(AlertService);

    constructor(
        private issueService: IssueService,
        public dialog: MatDialog,
        private isMobileService: IsMobileService,
        private sidebarsContainerService: SidebarsContainerService,
        public translate: TranslateService,
        private emailService: EmailService,
        private emailIntegrationService: EmailIntegrationService,
        private dialogService: DialogService,
        private socketMailService: SocketMailService,
        private route: ActivatedRoute,
        private clientsService: ClientsService
    ) {
        this.isMobile$ = this.isMobileService.isMobileView;
    }

    ngOnInit() {
        this.getMailServerId();
        this.setupContentChangeSubscription();

        if (this.isForwarded) {
            this.loadClientsEmails();
            this.filteredEmails = this.emailCtrl.valueChanges.pipe(
                startWith(''),
                map((email: string | null) => {
                    return email ? this._filter(email) : this.clientsEmails.slice();
                })
            );
        }
    }

    ngOnDestroy() {
        this.subscriptions?.unsubscribe();
        if (this.draftUpdateTimeout) {
            clearTimeout(this.draftUpdateTimeout);
        }
    }

    onEditorCreated(editor: any) {
        this.Quill = editor;
        this.editorInitialized = true;

        this.emailService.getFooter().subscribe((data) => {
            this.bodyFooter = data?.body;

            if (this.isForwarded) {
                const initialContent = this.mailData.mail_body || '';
                const forwardedHeader = `
                    <div style="margin-bottom: 20px; font-family: arial, sans-serif;">
                    ---------- Forwarded message ---------<br>
                    <b>${this.translate.instant('MAILS-VIEW.FROM')}:</b> ${this.mailData.sender_mail_address || this.mailData.receiver_mail_address}<br>
                    <b>${this.translate.instant('MAILS-VIEW.DATE')}:</b> ${dayjs(this.mailData.modified).format('DD.MM.YYYY')}<br>
                    <b>${this.translate.instant('MAILS-VIEW.SUBJECT')}:</b> ${this.mailData.mail_subject}<br>
                    <b>${this.translate.instant('MAILS-VIEW.TO')}:</b> ${this.mailData.mail_address}<br>
                    </div>
                  `;

                if (this.route.snapshot.data.name === MailRoutePath.DRAFT) {
                    this.mailData.mail_subject = 'Fwd: ' + this.mailData.mail_subject;
                }

                this.Quill.setContents([]);
                this.Quill.clipboard.dangerouslyPasteHTML(forwardedHeader + initialContent);
            } else {
                if (this.route.snapshot.data.name === MailRoutePath.DRAFT && this.mailData.mail_body) {
                    this.Quill.setContents([]);
                    const delta = this.Quill.clipboard.convert(this.mailData.mail_body);
                    this.Quill.setContents(delta);
                } else {
                    this.Quill.setContents([]);
                    this.Quill.clipboard.dangerouslyPasteHTML('');
                }
            }

            if (this.bodyFooter) {
                const currentContent = this.Quill.root.innerHTML;
                if (!currentContent.includes(this.bodyFooter)) {
                    const horizontalRule = '<br><br>';
                    this.Quill.clipboard.dangerouslyPasteHTML(
                        this.Quill.getLength(),
                        horizontalRule + this.bodyFooter
                    );
                }
            }

            this.initialContent = this.Quill.root.innerHTML;

            if (this.isForwarded || this.route.snapshot.data.name !== MailRoutePath.DRAFT) {
                this.createDraft();
            }
        });
    }

    onContentChanged(event: any) {
        const currentContent = event.html;

        if (this.draftUpdateTimeout) {
            clearTimeout(this.draftUpdateTimeout);
        }

        this.draftUpdateTimeout = setTimeout(() => {
            const isContentChanged = this.mailData.mail_body !== currentContent;

            if (this.route.snapshot.data.name === MailRoutePath.DRAFT && isContentChanged) {
                this.mailData.mail_body = currentContent;
            }

            if (isContentChanged) {
                this.updatedMailData = this.prepareMailData(currentContent);
                this.contentChangeSubject.next(currentContent);
                this.draftChange.emit(this.mailData);
            }
        }, this.DEBOUNCE_TIME);
    }

    private prepareMailData(content?: string) {
        return {
            MailSentMysqlMessage: {
                mail_id: this.draftId,
                mail_server_id: this.mailServerId,
                mail_address: this.mailData.mail_address,
                mail_subject: this.mailData.mail_subject,
                mail_to_name: this.mailData.mail_name,
                mail_body: content || this.Quill?.root?.innerHTML || '',
                status: QueueStatus.DRAFT
            }
        };
    }

    private createDraft() {
        this.updatedMailData = this.prepareMailData();
        if (this.isForwarded) {
            const forwardedDetails = this.getForwardedMailDetails();

            this.updatedMailData = {
                MailSentMysqlMessage: Object.assign(
                    {},
                    this.updatedMailData.MailSentMysqlMessage,
                    {...forwardedDetails}
                )
            };
        }

        this.emailService.createDraftMail(this.updatedMailData)
            .subscribe(response => {
                this.draftId = response['id'];
                this.alertService.showAlert(this.translate.instant('SEND-MESSAGE-DIALOG.DRAFT-CREATED'), AlertType.SUCCESS, AlertDuration.MEDIUM);
            }, error => {
                this.alertService.showAlert(this.translate.instant('SEND-MESSAGE-DIALOG.DRAFT-ERROR'), AlertType.ERROR, AlertDuration.MEDIUM);
            });
    }

    private setupContentChangeSubscription() {
        this.subscriptions.add(
            this.contentChangeSubject.pipe(
                debounceTime(this.DEBOUNCE_TIME),
                distinctUntilChanged()
            ).subscribe((currentContent) => {
                if (this.editorInitialized
                    && ((this.route.snapshot.data.name !== MailRoutePath.DRAFT && !this.draftId)
                        || (this.route.snapshot.data.name === MailRoutePath.DRAFT && +this.mailData.status !== QueueStatus.DRAFT))
                    && currentContent !== this.initialContent) {
                    this.createDraft();
                } else if (this.editorInitialized && (this.draftId || this.mailData.id)) {
                    if (!this.draftId && this.mailData.id) {
                        this.draftId = +this.mailData.id;
                    }
                    this.handleContentChange();
                }
            })
        );
    }

    private handleContentChange() {
        const draftId = this.draftId || this.mailData.id;
        if (draftId) {
            if (this.Quill.root.innerHTML !== this.initialContent) {
                this.emailService.updateDraftMail(this.updatedMailData, draftId)
                    .subscribe(
                        response => {
                            this.alertService.showAlert(this.translate.instant('SEND-MESSAGE-DIALOG.DRAFT-UPDATED'), AlertType.SUCCESS, AlertDuration.MEDIUM);
                        },
                        error => {
                            this.alertService.showAlert(this.translate.instant('SEND-MESSAGE-DIALOG.DRAFT-ERROR'), AlertType.ERROR, AlertDuration.MEDIUM);
                        }
                    );
            }
        }
    }

    onSelectionChanged() {
        if (this.Quill.hasFocus()) {
            this.issueService.setExpandedEditor(true);
        }
    }

    toggleExpandedFullEditor() {
        const isExpanded = this.expandedFullEditor$.getValue();
        if (!isExpanded) {
            this.sidebarsContainerService.sidebar('menu').compress();
            this.sidebarsContainerService.sidebar('users').compress();
        }
        this.expandedFullEditor$.next(!isExpanded);
    }

    onSubmit() {
        this.isSendButtonDisabled = true;
        this.editorInitialized = false;
        this.emailIntegrationService.verifyMailServerTokenStatus().subscribe((isVerified) => {
            if (!isVerified) {
                this.dialog.open(MailsIntegrationModalComponent, {
                    width: '480px',
                    disableClose: true
                });
                return;
            }

            const toAddress = this.isForwarded
                ? this.emails.join(',')
                : (Array.isArray(this.mailData.mail_address)
                    ? this.mailData.mail_address.join(',')
                    : this.mailData.mail_address);

            const toName = this.isForwarded
                ? this.emails
                : [this.mailData.mail_address];

            const forwardedDetails = this.getForwardedMailDetails();

            const mailData = {
                MailSend: {
                    mail_id: +this.draftId,
                    mail_server_id: +this.mailServerId,
                    mail_to_address: toAddress,
                    mail_subject: this.mailData.mail_subject,
                    mail_to_name: toName,
                    mail_body: this.Quill.root.innerHTML,
                    ...forwardedDetails
                }
            };

            this.emailService.sendMail(mailData).subscribe(
                () => {
                    this.Quill.setContents([]);
                    this.alertService.showAlert(this.translate.instant('SEND-MESSAGE-DIALOG.SUCCESSFULLY-SENT'), AlertType.SUCCESS, AlertDuration.MEDIUM);
                },
                error => {
                    console.error('Full Error Response:', error);
                    this.alertService.showAlert(this.translate.instant('SEND-MESSAGE-DIALOG.ERROR-SENT'), AlertType.ERROR, AlertDuration.MEDIUM);
                },
                () => {
                    this.dialogService.closeDialog();
                    if (+this.mailData.status === QueueStatus.DRAFT) {
                        this.emailService.removeDraft(+this.mailData.id).pipe(
                            tap(() => this.socketMailService.emitSentMailChange()),
                            first()
                        ).subscribe();
                    }
                }
            );
        });
    }

    getMailServerId() {
        this.emailService.getMailServer().subscribe(
            response => {
                const mailServerId = response['results'][0].MailServer?.id;
                if (mailServerId) {
                    this.mailServerId = +mailServerId;
                }
            },
            error => {
                console.error(error);
            }
        );
    }

    addEmail(event: MatChipInputEvent): void {
        const input = event.input;
        const value = (event.value || '').trim();
        if (value) {
            if (this.isValidEmail(value)) {
                if (!this.emails.includes(value)) {
                    this.emails.push(value);
                }
                if (input) {
                    input.value = '';
                }
                this.emailCtrl.setValue('');
            } else {
                event.value = value;
            }
        }
    }

    handleInputBlur(event: FocusEvent): void {
        const input = event.target as HTMLInputElement;
        const value = input.value.trim();
        if (value) {
            if (this.isValidEmail(value)) {
                if (!this.emails.includes(value)) {
                    this.emails.push(value);
                }
                if (input) {
                    input.value = '';
                }
                this.emailCtrl.setValue('');
            }
        }
    }

    handlePaste(event: ClipboardEvent): void {
        event.preventDefault();
        const pastedText = event.clipboardData?.getData('text') || '';
        const emails = pastedText.split(/[\s,;]+/).filter(text => text.trim() !== '');
        for (const email of emails) {
            if (this.isValidEmail(email)) {
                if (!this.emails.includes(email)) {
                    this.emails.push(email);
                }
            }
        }
        this.emailCtrl.setValue('');
    }

    removeEmail(email: string): void {
        const index = this.emails.indexOf(email);
        if (index >= 0) {
            this.emails.splice(index, 1);
        }
    }

    selectedEmail(event: MatAutocompleteSelectedEvent): void {
        const email = event.option.viewValue;
        if (!this.emails.includes(email)) {
            this.emails.push(email);
        }
        this.emailInput.nativeElement.value = '';
        this.emailCtrl.setValue(null);
    }

    private loadClientsEmails() {
        this.subscriptions.add(
            this.clientsService.getClients().subscribe((response: any) => {
                this.clientsEmails = response.items.map(client => client.email);
            })
        );
    }

    private _filter(value: string): string[] {
        const filterValue = value.toLowerCase();
        return this.clientsEmails.filter(email =>
            email.toLowerCase().includes(filterValue) &&
            !this.emails.includes(email)
        );
    }

    private isValidEmail(email: string): boolean {
        const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
        return emailRegex.test(email);
    }

    private getForwardedMailDetails(): {
        forwarded_mail_id: number | null,
        forwarded_from: MailType | null
    } {
        if (!this.isForwarded) {
            return {
                forwarded_mail_id: null,
                forwarded_from: null
            };
        }

        return {
            forwarded_mail_id: +this.mailData.id,
            forwarded_from: this.mailData.sender_mail_address
                ? MailType.SENT
                : (this.mailData.receiver_mail_address
                    ? MailType.RECEIVED
                    : null)
        };
    }

    private getFooterData() {
        this.emailService.getFooter().subscribe((data) => {
            this.bodyFooter = data.body;

            if (this.bodyFooter !== null && this.editorInitialized && this.Quill) {
                const currentContent = this.Quill.root.innerHTML;
                if (!currentContent.includes(this.bodyFooter)) {
                    const horizontalRule = '<br><br>';

                    this.Quill.clipboard.dangerouslyPasteHTML(
                        this.Quill.getLength(),
                        horizontalRule + this.bodyFooter
                    );

                    this.initialContent = this.Quill.root.innerHTML;
                    this.mailData.mail_body = this.initialContent;
                }
            }
        });
    }
}
