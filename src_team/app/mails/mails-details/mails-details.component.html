<div class="mails-view">
    <div class="header" appLucideIcons>
        @if (!draftView) {
            <div class="accordion-item">
                <button class="accordion-header" [ngClass]="{ 'open': isOpenAccordion }" (click)="toggleAccordion()">
                    <div class="accordion-header__left">
                        <ngx-avatars matTooltip="{{mailData?.first_name}}" [size]="28" [name]="mailData?.first_name" [bgColor]="defaultAvatarBgColor"></ngx-avatars>
                        <span>{{mailData.mail_name}}{{moreMailsDots}}</span>
                    </div>

                    <div class="accordion-header__right">
                        <span>{{ mailData?.timestamp | timestampToDate }}</span>
                        <i-lucide name='chevron-down' class="icon"></i-lucide>
                    </div>
                </button>
                <div class="accordion-content" [ngClass]="{ 'open': isOpenAccordion }">
                    <div class="accordion-content__left">
                        <div class="accordion-content__left__item">
                            <div class="accordion-content__left__item__title">{{'ISSUE-VIEW-SIDEBAR.PRIORITY' | translate}}</div>
                            <div class="accordion-content__left__item__description">
                                @if (+mailData?.priority) {
                                    {{'ISSUE-VIEW-SIDEBAR.YES' | translate}}
                                } @else {
                                    {{'ISSUE-VIEW-SIDEBAR.NO' | translate}}
                                }
                            </div>
                        </div>
                        <div class="accordion-content__left__item">
                            <div class="accordion-content__left__item__title">{{'MAILS-VIEW.CREATED' | translate}}:</div>
                            <div class="accordion-content__left__item__description">
                                @if (mailData?.timestamp) {
                                    {{ mailData?.timestamp | timestampToDate }}
                                }
                            </div>
                        </div>
                        <div class="accordion-content__left__item">
                            <div class="accordion-content__left__item__title">{{'MAILS-VIEW.RECEIVED' | translate}}:</div>
                            <div class="accordion-content__left__item__description">
                                @if (mailData?.created) {
                                    {{ mailData.created | date:'d MMMM yyyy HH:mm:ss' : 'pl' }}
                                }
                            </div>
                        </div>
                        <div class="accordion-content__left__item">
                            <div class="accordion-content__left__item__title">{{'MAILS-VIEW.FROM' | translate}}:</div>
                            <div class="accordion-content__left__item__description">
                                @if (mailData.mail_address || mailData.sender_mail_address) {
                                    {{isMailReceived ? mailData.mail_address : mailData.sender_mail_address}}
                                }
                            </div>
                        </div>
                        <div class="accordion-content__left__item">
                            <div class="accordion-content__left__item__title">{{'MAILS-VIEW.TO' | translate}}:</div>
                            <div class="accordion-content__left__item__description">
                                @if (mailData.mail_address || mailData.receiver_mail_address) {
                                    {{isMailReceived ? mailData.receiver_mail_address : mailData.mail_address}}
                                }
                            </div>
                        </div>
                        @if (mailData.cc_mail_addresses) {
                            <div class="accordion-content__left__item">
                                <div class="accordion-content__left__item__title">{{'MAILS-VIEW.CC' | translate}}:</div>
                                <div class="accordion-content__left__item__description">
                                    {{mailData.cc_mail_addresses}}
                                </div>
                            </div>
                        }
                        @if (mailData.bcc_mail_addresses) {
                            <div class="accordion-content__left__item">
                                <div class="accordion-content__left__item__title">{{'MAILS-VIEW.BCC' | translate}}:</div>
                                <div class="accordion-content__left__item__description">
                                    {{mailData.bcc_mail_addresses}}
                                </div>
                            </div>
                        }
                        <div class="accordion-content__left__item">
                            <div class="accordion-content__left__item__title">{{'MAILS-VIEW.SUBJECT' | translate}}:</div>
                            <div class="accordion-content__left__item__description">
                                @if (mailData.mail_subject) {
                                    {{mailData?.mail_subject}}
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>

    @if (!draftView) {
        <div class="body mail-mainblock-wrapper">
            <div class="body__header">
                <span class="body__header__id" ngxClipboard [cbContent]="mailData.id + ''" matTooltip="{{'MAILS-VIEW.ID' | translate}}"></span>
                <span class="body__header__title">
                    {{mailData?.mail_subject}}
                </span>
            </div>
            <div class="horizontal-separator"></div>
            <div class="body__content">
                <app-shadow-wrapper>
                    <app-mails-body [body]="mailData?.mail_body"></app-mails-body>
                </app-shadow-wrapper>
                @if (isMultipleValue(commonFileIdArr)) {
                    <app-mails-attached-files-list [commonFileObject]="commonFileAttachments"></app-mails-attached-files-list>
                } @else {
                    @if (commonFileId) {
                        <app-download-button
                            [fileName]="commonFileName"
                            [fileId]="commonFileId"
                            [fileType]="commonFileType">
                        </app-download-button>
                    }
                }
            </div>
            @if (+mailData?.missing_attachments_count) {
                <div class="body__editor__toolbar">
                    <span appLucideIcons class="missing-attachment">
                        <div>
                            <i-lucide name="alert-circle" class="icon warn-color"></i-lucide>
                        </div>
                        <div>
                            <span class="warn-color">{{ missingAttachmentCountPhrase }} </span>
                            <span>
                                {{'MAILS-VIEW.MISSING_ATTACHMENTS_COUNT.DESCRIPTION' | translate}}
                            </span>
                        </div>
                    </span>
                </div>
            }
            @if (forwardEditorVisible) {
                <div class="body__editor__toolbar">
                    <div class="body__editor__toolbar__left">
                        <app-5ways-button iconRight="send-horizontal" [variant]="ButtonVariant.GHOST" (click)="openEditor()">
                            {{'ISSUE-VIEW-SIDEBAR.ANSWER' | translate}}
                        </app-5ways-button>
                    </div>

                    <div class="body__editor__toolbar__right">
                        <app-5ways-button [iconSize]="24" iconRight="maximize" matTooltip="{{'ICONS.FULLSCREEN' | translate}}" [variant]="ButtonVariant.GHOST" (middleclick)="openFull()" (click)="openFull()">
                        </app-5ways-button>
                    </div>
                </div>
            }

            @if (isEditorOpen) {
                <div class="body__editor__toolbar">
                    <div class="body__editor__toolbar__left">
                        <app-5ways-button iconRight="forward" [variant]="ButtonVariant.GHOST" (click)="openForwardEditor()">
                            {{ 'MAIL-FORWARD-EDITOR.FORWARD' | translate }}
                        </app-5ways-button>
                    </div>

                    <div class="body__editor__toolbar__right">
                        <app-5ways-button [iconSize]="24" iconRight="maximize" matTooltip="{{'ICONS.FULLSCREEN' | translate}}" [variant]="ButtonVariant.GHOST" (middleclick)="openFull(true)" (click)="openFull(false)">
                        </app-5ways-button>
                    </div>
                </div>
            }

            <div class="body__editor" [ngClass]="{'body__editor__height': isEditorOpen || forwardEditorVisible}">
                @if (isEditorOpen) {
                    <app-mails-editor
                        [mailData]="mailData"
                        [isForwarded]="false">
                    </app-mails-editor>
                }

                @if (forwardEditorVisible) {
                    <app-mails-editor
                        [mailData]="mailData"
                        [isForwarded]="true">
                    </app-mails-editor>
                }

                @if (!isEditorOpen && !forwardEditorVisible) {
                    <div class="body__editor__toolbar">
                        <div class="body__editor__toolbar__left">
                            <app-5ways-button iconRight="send-horizontal" [variant]="ButtonVariant.GHOST" (click)="openEditor()">
                                {{'ISSUE-VIEW-SIDEBAR.ANSWER' | translate}}
                            </app-5ways-button>
                            <app-5ways-button iconRight="forward" [variant]="ButtonVariant.GHOST" (click)="openForwardEditor()">
                                {{ 'MAIL-FORWARD-EDITOR.FORWARD' | translate }}
                            </app-5ways-button>
                        </div>

                        <div class="body__editor__toolbar__right">
                            <app-5ways-button [iconSize]="24" iconRight="maximize" matTooltip="{{'ICONS.FULLSCREEN' | translate}}" [variant]="ButtonVariant.GHOST" (middleclick)="openFull(true)" (click)="openFull(false)">
                            </app-5ways-button>
                        </div>
                    </div>
                }
            </div>
        </div>

        <div class="footer">
            <div class="footer__left">
                <app-tags class="mt-4" place="mail" [objectName]="tagObjectName" [tagsIds]="mailData.tags" [objectId]="mailData.id" [editMode]="tagsEditMode"></app-tags>
            </div>
            <div class="footer__right">
                <app-more-options [menuItems]="moreOptionsMenu"></app-more-options>
            </div>
        </div>
    } @else {
        <div class="body mail-mainblock-wrapper draft">
            <div class="body__editor body__editor__height">
                <mat-form-field class="full-width" [floatLabel]="mailAddressArray.length ? 'always' : 'auto'">
                    <mat-label>{{ 'SEND-MESSAGE-DIALOG.SENDERS' | translate }}</mat-label>
                    <mat-chip-grid
                        #chipList
                        aria-label="Address selection"
                        (focusin)="hasMailsInputFocus = true"
                        (focusout)="handleMailsInputBlur($event)"
                    >
                        @if (hasMailsInputFocus) {
                            @for (sender of mailAddressArray; track sender) {
                                <mat-chip [removable]="true">
                                    {{ sender }}
                                    <mat-icon
                                        matChipRemove
                                        (mousedown)="removeEmailAddress(sender, $event)"
                                    >
                                        cancel
                                    </mat-icon>
                                </mat-chip>
                            }
                        }

                        @if (!hasMailsInputFocus) {
                            @for (sender of visibleSenders; track sender) {
                                <mat-chip [removable]="false">
                                    {{ sender }}
                                </mat-chip>
                            }
                            @if (mailAddressArray.length > maxVisibleSenders) {
                                <mat-chip>
                                    {{ 'SEND-MESSAGE-DIALOG.PLUS-EMAILS' | translate }}
                                    {{ mailAddressArray.length - maxVisibleSenders }}
                                </mat-chip>
                            }
                        }

                        <input
                            matInput
                            #sendersInput
                            [matAutocomplete]="auto"
                            [matChipInputFor]="chipList"
                            [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                            (matChipInputTokenEnd)="addEmailAddress($event)"
                            (blur)="handleInputBlur($event)"
                            (focus)="hasMailsInputFocus = true"
                            (click)="autocompleteTrigger.openPanel()"
                        />
                    </mat-chip-grid>
                </mat-form-field>
                <mat-autocomplete
                    #auto="matAutocomplete"
                    (optionSelected)="onEmailSelected($event)"
                >
                    @for (client of filteredClients | async; track client) {
                        <mat-option [value]="client">
                            {{ client }}
                        </mat-option>
                    }
                </mat-autocomplete>

                <mat-form-field class="full-width my-2">
                    <mat-label>{{ 'SEND-MESSAGE-DIALOG.TOPIC' | translate }}</mat-label>
                    <input
                        matInput
                        [(ngModel)]="mailData.mail_subject"
                        autocomplete="off"
                        (input)="onTopicChange()"
                    />
                </mat-form-field>

                <app-mails-editor
                    [mailData]="mailData"
                    (draftChange)="onDraftChanged($event)"
                >
                </app-mails-editor>
            </div>
        </div>
    }
</div>
