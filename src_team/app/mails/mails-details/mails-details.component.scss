@use '../../../variables' as *;

.mails-view {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100vh;
    overflow: hidden;

    .header {
        width: 100%;
        min-height: 64px;
        border-radius: 4px;
        overflow: hidden;
        flex-shrink: 0;

        .accordion-item {
            border-bottom: 1px solid $fiveways-stroke-2;
        }

        .accordion-header {
            width: 95%;
            padding: 18px 24px 18px 24px;
            text-align: left;
            background-color: $fiveways-white;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;

            &__left {
                display: flex;
                align-items: center;
                gap: 10px;
                font-family: Manrope;
                font-size: 16px;
                font-style: normal;
                font-weight: 700;
                line-height: normal;
                letter-spacing: 0.32px;
                color: $fiveways-gray;
            }

            &__right {
                display: flex;
                align-items: center;
                gap: 8px;
                font-family: Manrope;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                letter-spacing: 0.24px;
                color: $fiveways-gray;

                .icon {
                    transition: transform 0.3s ease;
                }
            }

            &.open {
                .icon {
                    transform: rotate(180deg);
                }
            }
        }

        .accordion-content {
            display: flex;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out, padding 0.3s ease-out;
            background-color: $fiveways-header-bg;
            padding: 0 24px;

            p {
                margin: 15px 0;
            }

            &.open {
                max-height: 620px;
                padding: 24px;
            }

            &__left {
                display: flex;
                flex-direction: column;
                gap: 8px;

                &__item {
                    display: flex;
                    flex-direction: row;
                    align-items: center;

                    &__title {
                        font-family: Manrope;
                        font-size: 11px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: normal;
                        letter-spacing: 0.22px;
                    }

                    &__description {
                        margin-left: 8px;
                        font-family: Manrope;
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 700;
                        line-height: normal;
                        letter-spacing: 0.24px;
                    }
                }
            }
        }
    }

    .body {
        display: flex;
        flex-direction: column;
        gap: 16px;
        width: 100%;
        flex: 1;
        min-height: 0;
        padding: 24px 24px 15px 24px;

        &__header {
            width: 100%;
            height: 22px;
            display: flex;
            flex-direction: row;
            gap: 8px;
            border-left: 2px solid $fiveways-warning;
            font-family: Manrope;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            letter-spacing: 0.32px;
            padding-left: 8px;
            color: $fiveways-gray;

            &__id {
                font-family: Manrope;
                font-size: 16px;
                font-style: normal;
                font-weight: 700;
                line-height: normal;
                letter-spacing: 0.32px;
                color: $fiveways-gray;
                cursor: pointer;
            }

            .vertical-separator {
                width: 1px;
                height: 100%;
                background-color: $fiveways-stroke-2;
            }

            &__title {
                font-family: Manrope;
                font-size: 14px;
                font-style: normal;
                font-weight: 700;
                line-height: normal;
                letter-spacing: 0.28px;
            }
        }

        .horizontal-separator {
            width: 100%;
            height: 1px;
            background-color: $fiveways-stroke-2;
        }

        .body {
            display: flex;
            flex-direction: column;
            flex: 1;
            min-height: 331px;
            align-items: center;

            .mail-mainblock-wrapper {
                background: #F5F5F5;
                height: 100%;
                position: relative;

                &.blocked {
                    height: auto;
                    min-height: 100%;
                    pointer-events: none;
                }
            }

            &__content {
                display: flex;
                flex-direction: column;
                min-height: 100px;
                max-height: 75%;
                overflow-y: auto;
                font-family: Manrope;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                letter-spacing: 0.16px;
                color: $fiveways-gray;
            }

            &__editor {
                flex: 1;
                min-height: 58px;
                position: relative;
                overflow: hidden;

                &__height {
                    overflow: hidden;
                    top: 0;
                    min-height: 330px;
                    padding: 2px;
                }

                &__toolbar {
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;
                    min-height: 48px;
                    height: auto;
                    border: 1px solid $fiveways-stroke-2;
                    border-radius: 8px;
                    padding: 8px;

                    &__left {

                    }

                    &__right {
                        pointer-events: all;
                        z-index: 1;
                    }

                    .missing-attachment {
                        display: flex;
                        justify-content: space-between;
                        margin: 8px 4px;
                        color: $fiveways-gray-80;
                        font-size: 14px;
                        line-height: 1.5em;

                        div {
                            margin: 2px 3px;
                        }

                        .warn-color {
                            font-weight: bold;
                        }

                        .icon {
                            margin-top:2px;
                            width: 16px;
                            height: 16px;
                        }
                    }
                }
            }
        }
    }

    .mail-mainblock-wrapper {
        position: relative;
        width: 100%;
        height: 100%;

        .block-text {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            margin: auto;
            height: fit-content;
            width: fit-content;
            color: #fff;
            z-index: 5;
            font-family: Manrope, sans-serif;
            font-size: 16px;
            font-style: normal;
            font-weight: 700;
            line-height: normal;
            letter-spacing: 0.28px;
            pointer-events: all;

            .take-button {
                display: flex;
                justify-content: center;
                margin-top: 4px;
            }
        }

        &:is(.blocked) {
            pointer-events: none;
        }

        &.blocked {
            &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.6);
                pointer-events: none;
                z-index: 9999;
            }
        }

        &.draft {
            border-top: 1px solid $fiveways-stroke-2;
            padding-top: 58px;
        }
    }

    .footer {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
        height: 80px;
        padding: 24px;
        background-color: $fiveways-white;
        z-index: 10;

        &__left {
            display: flex;
            flex-direction: row;
            gap: 8px;
        }

        &__right {
            display: flex;
            flex-direction: row;
            gap: 8px;
        }
    }
}

.editor {
    height: 100%;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    padding-bottom: calc(80px + 24px);
}
