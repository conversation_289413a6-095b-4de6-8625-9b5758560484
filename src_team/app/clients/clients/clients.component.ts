import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, forwardRef, Input, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import { ClientsService } from '../../services/clients.service';
import { SanitizeColumnIdPipe } from '../../shared/pipes/sanitize-column-id.pipe';
import { PageEvent } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { SelectionModel } from '@angular/cdk/collections';
import { MatDialog } from '@angular/material/dialog';
import { IssueInitiator } from '../../common/interfaces/issue-initiator.interface';
import { UserInputStorageService } from '../../services/user-input-storage.service';
import { Subscription, timer } from 'rxjs';
import { IsMobileService } from '../../services/is-mobile.service';
import { HttpParams } from '@angular/common/http';
import { SendMessageDialogComponent } from '../../mails/send-message-dialog/send-message-dialog.component';
import { TranslateService, TranslatePipe } from '@ngx-translate/core';
import { ColDef, GridOptions } from 'ag-grid-community';
import { getClientsColumnDefs } from '../client-config/client-columns.config';
import { ButtonVariant } from '../../common/enums/button-variant.enum';
import { PermissionService } from '../../services/permission.service';
import { BASE_GRID_OPTIONS } from '../../shared/table/base-grid-options';
import { EmailIntegrationService } from '../../services/email-integration.service';
import { selectMailServerId } from '../../store/mail-server/mail-server.selectors';
import { Store } from '@ngrx/store';
import { switchMap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { ClientViewService} from '../../services/client-view.service';
import { ClientNipInputComponent } from './client-nip-input/client-nip-input.component';
import { ClientNipInputAction } from '../../common/enums/client-nip-input-action.enum';
import {TopMenuService} from '../../services/top-menu.service';
import {TopMenuAction} from '../../common/interfaces/top-menu-action.interface';
import { DialogDetailsComponent } from '../../shared/dialog-details/dialog-details.component';
import { NgIf } from '@angular/common';
import { ButtonComponent } from '../../elements/button/button.component';
import { PaginatorComponent } from '../../elements/paginator/paginator.component';
import { DefaultShowHideDirective } from 'ngx-flexible-layout/extended';
import { SearchInputComponent } from '../../elements/search-input/search-input.component';
import { FormsModule } from '@angular/forms';
import { ClientStatusFilterComponent } from '../../shared/client-status-filter/client-status-filter.component';
import { ClientTypeFilterComponent } from '../../shared/client-type-filter/client-type-filter.component';
import { AgGridModule } from 'ag-grid-angular';
import { CheckPermissionNamePipe } from '../../shared/pipes/check-permission-name.pipe';

@Component({
    selector: 'app-clients',
    templateUrl: './clients.component.html',
    styleUrls: ['./clients.component.scss'],
    imports: [forwardRef(() => DialogDetailsComponent), NgIf, PaginatorComponent, DefaultShowHideDirective, SearchInputComponent, FormsModule, ClientStatusFilterComponent, ClientTypeFilterComponent, AgGridModule, TranslatePipe]
})
export class ClientsComponent implements OnInit, DoCheck, OnDestroy {

    @Input()
        listName: string;

    @Input() set initialSortField(value: string) {
        this.sortField = value;
    }

    @Input() set initialSortType(value: string) {
        this.sortType = value;
    }

    clients: IssueInitiator[] = [];
    dataSource = new MatTableDataSource<IssueInitiator>(this.clients);
    clientSelection = new SelectionModel<IssueInitiator>(true, []);
    limit: number;
    page: number = 1;
    totalClients: number;
    sortField = 'first_name';
    sortType = 'asc';
    filtersCounter = 1;
    filters: any;
    isMobile: any;
    selectedIds: string[] = [];
    clientNameFilter = '';
    selectedEmails: string[] = [];
    columnDefs: ColDef[];
    defaultColDef: ColDef = {
        flex: 1,
        sortable: true,
        sortingOrder: ['asc', 'desc'],
        unSortIcon: false,
        filter: true,
        resizable: true,
        cellStyle: {
            padding: '12px 16px',
            fontSize: '14px',
            fontWeight: '400',
            letterSpacing: '0.28px'
        }
    };
    gridOptions: GridOptions = {
        ...BASE_GRID_OPTIONS,
        tooltipShowDelay: 0,
        suppressNoRowsOverlay: true,
        suppressHorizontalScroll: true,
        // AG Grid v33 - Use autoHeight for automatic sizing
        domLayout: 'autoHeight',
        // Set a consistent row height
        rowHeight: 48
    };
    isDetailsOpen = false;
    detailsData: any;
    detailsModel = 'clients';
    mailServerId: number;
    private subscriptions = new Subscription();
    readonly ButtonVariant = ButtonVariant;

    getRowStyle = (params) => {
        return {
            cursor: 'pointer',
            backgroundColor: params.node.selected ? '#F3F7FB' : '#FBFBFB',
            borderBottom: '1px solid #DBDFF7',
        };
    };

    constructor(
        private router: Router,
        private clientsService: ClientsService,
        private dialog: MatDialog,
        private userInputStorageService: UserInputStorageService,
        private isMobileService: IsMobileService,
        private translate: TranslateService,
        private permissionService: PermissionService,
        private store: Store,
        private emailIntegrationService: EmailIntegrationService,
        private clientViewService: ClientViewService,
        private sanitizeColumnIdPipe: SanitizeColumnIdPipe,
        private topMenuService: TopMenuService,
    ) {
        this.clientSelection = new SelectionModel<IssueInitiator>(true, []);
        this.selectedIds = [];

        this.subscriptions.add(
            this.clientViewService.clientEdit$
                .subscribe((result) => {
                    if (result.isOpen) {
                        const delayTime = 300;

                        this.isDetailsOpen = false,
                            timer(delayTime).subscribe(() => {
                                this.detailsData = result.data;
                                this.detailsModel = 'client-add';
                                this.isDetailsOpen = true;
                            });
                    }
                })
        );
    }

    ngOnInit(): void {
        this.isMobileService.isXS.subscribe(isXS => {
            this.isMobile = isXS;
        });

        this.filters = {
            type: {
                name: 'user_client_id',
                value: null,
                filter: 'gte_0'
            },
            status: {
                name: 'is_disabled',
                value: 0,
                filter: 'lte_0',
                mode: 'active'
            },
            nip: {
                name: 'nip',
                value: null,
                filter: null
            }
        };

        this.getPaginationLimit();

        this.subscriptions.add(
            this.clientsService.clientUpdated$.subscribe(() => {
                this.getClientsList();
            })
        );

        this.subscriptions.add(
            this.emailIntegrationService.checkEmailIntegrationStatus().pipe(
                switchMap(() => this.store.select(selectMailServerId))
            ).subscribe(id => {
                this.mailServerId = id;
            })
        );

        this.columnDefs = getClientsColumnDefs(
            this.translate,
            this.openClientDetails.bind(this),
        );

        this.getClientsList();
        this.catchTopMenuAction();
    }

    ngDoCheck(): void {
        this.updateSelectionClients();
    }

    ngOnDestroy() {
        this.topMenuService.setSelectedMode(false);
        this.subscriptions?.unsubscribe();
        this.resetSelection();
    }

    public catchTopMenuAction(): void {
        this.subscriptions.add(this.topMenuService.topMenuActions$.subscribe((action: TopMenuAction) => {
            switch (action.type) {
                case 'ADD':
                    this.addClient();

                    break;
                case 'SEND_MAIL':
                    this.openSendMessageDialog();

                    break;
                case 'SEND_ISSUE':
                    this.redirectToAddIssue();

                    break;
            }
        }));
    }

    public redirectToAddIssue(): void {
        this.router.navigate(['issue/add-batch'], { queryParams: { clients: this.selectedIds } });
    }

    onSelectionChanged(event) {
        this.selectedIds = event.api.getSelectedRows().map(row => row.id);
        this.selectedEmails = event.api.getSelectedRows().map(row => row.email);
        this.topMenuService.setSelectedMode(!!this.selectedIds.length);
    }

    applySort(event) {
        setTimeout(() => {
            let sortModel;
            try {
                const columnApi = event.api.getColumnApi?.() || event.api;
                sortModel = columnApi.getColumnState?.() || [];

                sortModel = sortModel.filter(column => column.sort);
            } catch (error) {
                console.warn('Error getting sort state:', error);
                sortModel = [];
            }

            if (sortModel && sortModel.length > 0) {
                // Sanitize the column ID by removing any numeric suffix (e.g., "_1", "_2")
                this.sortField = this.sanitizeColumnIdPipe.transform(sortModel[0].colId);

                this.sortType = sortModel[0].sort;
                this.getClientsList();
            }
        });
    }

    onPageEvent(event: PageEvent): void {
        this.page = event.pageIndex + 1;
        this.limit = event.pageSize;
        this.setPaginationLimit(this.limit);
        this.getClientsList();
    }

    private getPaginationLimit() {
        const paginationLimit = this.userInputStorageService.getValue('issueList_paginationLimit');
        this.limit = paginationLimit ? +paginationLimit : 10;
    }

    private setPaginationLimit(limit = 10) {
        this.limit = limit;
        this.userInputStorageService.setValue('issueList_paginationLimit', limit);
    }

    addClient() {
        const nipDialogRef = this.dialog.open(ClientNipInputComponent, {
            width: '500px',
            autoFocus: true,
            disableClose: false,
            panelClass: 'full-width-dialog'
        });

        nipDialogRef.afterClosed().subscribe(result => {
            if (result) {
                if (result.action === ClientNipInputAction.MANUAL) {
                    this.openClientAddDialog();
                } else if (result.action === ClientNipInputAction.SUCCESS) {
                    this.openClientAddDialog(result.data);
                }
            }
        });
    }

    private openClientAddDialog(clientData?) {
        const delayTime = 300;

        this.isDetailsOpen = false,
            timer(delayTime).subscribe(() => {
                this.detailsData = clientData;
                this.detailsModel = 'client-add';
                this.isDetailsOpen = true;
            });
    }

    getClientsList() {
        this.clientsService.getClients(this.requestConditions).subscribe(
            (clients) => {
                this.clients = clients.items;
                this.totalClients = clients.totalCount;
                this.dataSource = new MatTableDataSource<IssueInitiator>(this.clients);
                this.dataSource.data.forEach(row => {
                    if (this.selectedIds.includes(row.id ?? '')) {
                        this.clientSelection.select(row);
                    }
                });
            },
            error => {
                console.error('Wystąpił błąd przy wyświetlaniu listy klientów:', error);
            }
        );
    }

    private get requestConditions(): HttpParams {
        let params = new HttpParams();
        params = params.set('?', '');

        if (this.clientNameFilter) {
            params = params.set('search', this.clientNameFilter);
        }

        if (this.filters.nip.filter !== null) {
            params = params.set(this.filters.nip.name, this.filters.nip.filter);
        }

        if (this.filters.status.value !== null) {
            params = params.set(this.filters.status.name, this.filters.status.filter);
        }

        if (this.filters.type.value !== null) {
            params = params.set(this.filters.type.name, this.filters.type.filter);
        }

        params = params
            .set('search_in', 'name,first_name,last_name,phone,email')
            .set('page', this.page.toString())
            .set('limit', this.limit.toString())
            .set('order', `${this.sortField}|${this.sortType}`);

        return params;
    }

    onTypeChange(typeId: number) {
        this.filters.type.value = typeId;

        switch (typeId) {
            case 0:
                this.filters.type.filter = 'like';

                break;
            case 1:
                this.filters.type.filter = 'gte_1';

                break;
            default:
                this.filters.type.filter = 'gte_0';

                break;
        }

        this.setFiltersCounter();
        this.getClientsList();
    }

    onStatusChange(typeId: number) {
        this.filters.status.value = typeId;

        switch (typeId) {
            case 0:
                this.filters.status.mode = 'active';
                this.filters.status.filter = 'lte_0';

                break;
            case 1:
                this.filters.status.mode = 'blocked';
                this.filters.status.filter = 'gte_1';

                break;
            default:
                this.filters.status.mode = 'active';
                this.filters.status.filter = 'lte_0';

                break;
        }

        this.setFiltersCounter();
        this.getClientsList();
    }

    onNipChange(typeId: number) {
        this.filters.nip.value = typeId;

        switch (typeId) {
            case 0:
                this.filters.nip.filter = 'isn';

                break;
            case 1:
                this.filters.nip.filter = 'gte_1';

                break;
            default:
                this.filters.nip.filter = null;

                break;
        }

        this.setFiltersCounter();
        this.getClientsList();
    }

    private setFiltersCounter() {

        this.filtersCounter = Object.keys(this.filters)
            .filter((key: string) => {
                const item: any = this.filters[key];

                switch (typeof item.value) {
                    case 'string':
                        return item.value !== '';
                    case 'number':
                        return item.value !== null;
                    case 'boolean':
                        return item.value === true;
                }
            }).length;
    }

    openSendMessageDialog() {
        const delayTime = 300;
        this.isDetailsOpen = false;
        timer(delayTime).subscribe(() => {
            this.detailsModel = 'send-message';
            this.isDetailsOpen = true;
            this.detailsData = {selectedEmails: this.selectedEmails};
        });
    }

    private updateSelectionClients() {
        this.selectedIds = Array.from(new Set(this.selectedIds));

        this.dataSource.data.forEach((row) => {
            const isSelected = this.selectedIds.includes(row.id ?? '');

            if (isSelected && !this.clientSelection.isSelected(row)) {
                this.clientSelection.select(row);
            } else if (!isSelected && this.clientSelection.isSelected(row)) {
                this.clientSelection.deselect(row);
            }
        });
    }

    resetSelection() {
        this.clientSelection.clear();
        this.selectedIds = [];
        this.selectedEmails = [];
    }

    searchByName() {
        this.getClientsList();
    }

    hasPermission() {
        return this.permissionService.checkPermission('clientsListManagement');
    }

    openClientDetails(clientDetails) {
        const delayTime = 300;

        +this.detailsData?.id !== +clientDetails?.data?.id
            ? (
                this.isDetailsOpen = false,
                    timer(delayTime).subscribe(() => {
                        this.detailsData = clientDetails.data;
                        this.detailsModel = 'clients';
                        this.isDetailsOpen = true;
                    })
            )
            : (this.isDetailsOpen = !this.isDetailsOpen);
    }
}
