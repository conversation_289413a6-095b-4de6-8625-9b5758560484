@use '../../../variables' as *;

.clients {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  flex-wrap: wrap;
  margin-bottom: 14px;

  &__filters {
    gap: 10px;
  }
}

.table-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.table-wrapper {
  flex-grow: 1;
  margin: 0 10px 0 10px;
}

.no-data {
  font-size: 30px;
  font-weight: bold;
  letter-spacing: 2px;
  opacity: 0.2;
  display: flex;
  justify-content: center;
  height: 60vh;
  align-items: center;
}
