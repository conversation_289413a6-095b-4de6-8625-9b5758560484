import { TranslateService } from '@ngx-translate/core';
import { CellClickedEvent, ColDef, ValueGetterParams } from 'ag-grid-community';

import { isNullOrWhitespace } from '../../common/utils/string-utils';
import { BlockedInfoRendererComponent } from '../../components/blocked-info-renderer/blocked-info-renderer.component';
import { ClientTagsRendererComponent } from '../../components/client-tags-renderer/client-tags-renderer.component';
import { IssueMoreOptionsRendererComponent } from '../../components/more-options-renderer/issue-more-options-renderer.component';

const baseCellStyle = {
    borderLeft: '1px solid #DBDFF7',
    borderBottom: '1px solid #DBDFF7',
    padding: '12px 16px',
    fontFamily: 'Manrope',
    fontSize: '14px',
    fontWeight: '400',
    letterSpacing: '0.28px'
};

const cellStyles = {
    first: { ...baseCellStyle },
    middle: { ...baseCellStyle },
    last: { ...baseCellStyle, borderRight: '1px solid #DBDFF7' }
};

/**
 * Zwraca definicję kolumn dla klientów.
 *
 * Uwagi do użycia klasy CSS 'custom-ellipsis-cell':
 * - stosowane dla komórek, które mogą mieć długi ciąg znaków
 * - wymagane jest nadpisanie metody 'cellRenderer' w celu dodania kontenera z klasą 'cell-content',
 *   ponieważ sama komórka ma ustawiony 'display: flex', co uniemożliwia użycie `text-overflow: ellipsis`.
 */
export function getClientsColumnDefs(
    translate: TranslateService,
    openClientDetails: (event: CellClickedEvent) => void,
): ColDef[] {
    return [
        {
            field: 'name',
            headerName: translate.instant('CLIENTS-LIST.COMPANY-NAME'),
            minWidth: 250,
            flex: 2,
            sortable: true,
            filter: false,
            cellClass: 'custom-ellipsis-cell',
            valueGetter: (params: ValueGetterParams) => {
                const companyName: string = params.data?.name;
                const firstName: string = params.data?.first_name ?? '';
                const lastName: string = params.data?.last_name ?? '';

                const name: string = (!companyName || companyName === 'undefined') ? `${lastName} ${firstName}` : companyName;

                return isNullOrWhitespace(name) ? '' : name;
            },
            // cellRenderer potrzebny jest dla działania custom-ellipsis-cell (patrz komentarz "getClientsColumnDefs")
            cellRenderer: (params) => {
                return `<div class="cell-content">${params.value}<div>`;
            },
            cellStyle: cellStyles.middle,
            onCellClicked: openClientDetails,
        },
        {
            field: 'email',
            headerName: translate.instant('CLIENTS-LIST.E-MAIL'),
            minWidth: 250,
            flex: 2,
            sortingOrder: ['asc', 'desc'],
            sortable: true,
            cellClass: 'custom-ellipsis-cell',
            filter: false,
            icons: {
                sortAscending: '<i-lucide name="chevron-up" style="color: #1A9267; width: 12px; height: 16px; stroke-width: 3px; display: flex; align-items: center;"></i-lucide>',
                sortDescending: '<i-lucide name="chevron-down" style="color: #1A9267; width: 12px; height: 16px; stroke-width: 3px; display: flex; align-items: center;"></i-lucide>',
                sortUnSort: ''
            },
            cellStyle: cellStyles.middle,
            cellRenderer: BlockedInfoRendererComponent,
            onCellClicked: openClientDetails
        },
        {
            field: 'phone',
            headerName: translate.instant('CLIENTS-LIST.PHONE'),
            minWidth: 200,
            flex: 1,
            sortable: false,
            filter: false,
            cellStyle: cellStyles.middle,
            onCellClicked: openClientDetails
        },
        {
            field: 'tags',
            headerName: translate.instant('CLIENTS-LIST.LABELS'),
            minWidth: 300,
            flex: 1,
            sortable: false,
            filter: false,
            cellStyle: cellStyles.middle,
            cellRenderer: ClientTagsRendererComponent
        },
        {
            field: 'menu',
            headerName: '',
            minWidth: 50,
            maxWidth: 50,
            sortable: false,
            filter: false,
            resizable: false,
            suppressMovable: true,
            cellStyle: cellStyles.last,
            cellRenderer: IssueMoreOptionsRendererComponent,
            cellRendererParams: (params) => {
                const hasPermission = params.context.componentParent.hasPermission();

                return {
                    allowedActions: {
                        take: false,
                        delegate: false,
                        close: false,
                        open: false,
                        block: hasPermission
                    },
                    refreshList: () => params.context.componentParent.getClientsList()
                };
            }
        }

    ];
}
