import {Injectable} from '@angular/core';
import {combineLatest, Observable, of} from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import {MAILOAuth2Data, IMAPData, MailFooter, MailServer, TestMail, MailMailbox} from '../common/interfaces/email-integration.interface';
import {catchError, map, switchMap} from 'rxjs/operators';
import {environment} from '../../environments/environment';
import {DataResponseInterface} from '../common/interfaces/data-response.interface';
import {setMailServerState} from '../store/mail-server/mail-server.actions';
import {Store} from '@ngrx/store';
import {EMAIL_PROTOCOLS, ProtocolName, ServerProtocol} from '../common/enums/email-integration.enum';
import {selectIsMailServerTokenRelated, selectIsRefreshTokenActive} from '../store/mail-server/mail-server.selectors';

@Injectable({
    providedIn: 'root'
})
export class EmailIntegrationService {
    endpointUrl = environment.apiUrl + 'mail_server';
    mailServerAddressUrl = environment.apiUrl + 'mail_server_address';
    mailboxesUrl = environment.apiUrl + 'mail_mailbox';
    footerEndpointUrl = environment.apiUrl + 'mail_footer';
    testMailEndpointUrl = environment.apiUrl + 'mail_send_test';
    TOKEN_RELATED_PROTOCOLS: ServerProtocol[] = [
        ServerProtocol.GMAIL
    ];

    constructor(
        private _http: HttpClient,
        private store: Store
    ) {
    }

    checkEmailIntegrationStatus(): Observable<void> {
        return this._http.get<{ results: { MailServer: MailServer }[] }>(this.endpointUrl).pipe(
            map(response => response.results[0]?.MailServer ?? null),
            map(mailServer => {
                if (mailServer && mailServer?.server_protocol) {
                    const serverProtocol: number = Number(mailServer.server_protocol);
                    this.store.dispatch(setMailServerState({
                        id: mailServer.id,
                        is_mail_server_token_related: this.isMailServerTokenRelated(serverProtocol),
                        is_refresh_token_active: !!mailServer.refresh_token,
                        server_protocol_name: this.getProtocolName(serverProtocol)
                    }));
                }
            }),
            catchError(() => {
                this.store.dispatch(setMailServerState({
                    id: null,
                    is_mail_server_token_related: false,
                    is_refresh_token_active: null,
                    server_protocol_name: null
                }));
                return of(null);
            })
        );
    }

    verifyMailServerTokenStatus(): Observable<boolean> {
        return combineLatest([
            this.store.select(selectIsMailServerTokenRelated),
            this.store.select(selectIsRefreshTokenActive)
        ]).pipe(
            switchMap(([isTokenRelated, isRefreshTokenActive]) => {
                if (isTokenRelated && !isRefreshTokenActive) {
                    return of(false);
                }
                return of(true);
            })
        );
    }

    postIntegrationData(data: MailServer | IMAPData | MAILOAuth2Data): Observable<MailServer> {
        return this._http.post<MailServer>(this.endpointUrl, {MailServer: data});
    }

    updateIntegrationData(id: number, data: MailServer | IMAPData | MAILOAuth2Data): Observable<MailServer> {
        const url = `${this.endpointUrl}/${id}`;
        return this._http.put<MailServer>(url, {MailServer: data});
    }

    deleteIntegrationData(id: number): Observable<MailServer> {
        const url = `${this.endpointUrl}/${id}`;
        return this._http.delete<MailServer>(url);
    }

    getIntegrationSettingsData(protocolType: number): Observable<MailServer[]> {
        const params = new HttpParams().set('server_protocol', protocolType.toString());
        return this._http.get(this.endpointUrl, { params })
            .pipe(map((response: { results, total: number }) => response.results?.map(element => element.MailServer)));
    }

    getIntegrationMailsFetchingData(userId: number): Observable<MailServer[]> {
        const params = new HttpParams().set('user_id', userId.toString());
        return this._http.get(this.endpointUrl, { params })
            .pipe(map((response: { results, total: number }) => response.results.map(element => element.MailServer)));
    }

    getMailboxData(userId: number): Observable<MailMailbox[]> {
        if (!userId) {
            return of([]);
        }
        const params = new HttpParams().set('user_id', userId?.toString());
        return this._http.get(this.mailboxesUrl, { params })
            .pipe(map((response: { results, total: number }) => response.results?.map(element => element.MailMailbox)));
    }

    postDataToGetEmail(data: TestMail) {
        return this._http.post(this.testMailEndpointUrl, {MailSendTest: data});
    }

    postFooterData(data: MailFooter): Observable<MailFooter> {
        return this._http.post<MailFooter>(this.footerEndpointUrl, {MailFooter: data});
    }

    getFooterData(): Observable<MailFooter[]> {
        return this._http.get(this.footerEndpointUrl)
            .pipe(map((response: DataResponseInterface) => response.results.map(element => element.MailFooter)));
    }

    updateFooterData(id: number, data: MailFooter): Observable<MailFooter> {
        const url = `${this.footerEndpointUrl}/${id}`;

        return this._http.put<MailFooter>(url, {MailFooter: data});
    }

    private isMailServerTokenRelated(serverProtocol: number): boolean {
        return this.TOKEN_RELATED_PROTOCOLS.includes(serverProtocol);
    }

    private getProtocolName(value: ServerProtocol): ProtocolName | undefined {
        const protocol = EMAIL_PROTOCOLS.find(protocol => protocol.value === value);
        return protocol ? protocol.key : undefined;
    }

    getEmailIntegrationAddresses(): Observable<MailServer[]> {
        return this._http.get(this.mailServerAddressUrl)
            .pipe(map((response: { results, total: number }) => response.results.map(element => element.MailServer)));
    }
}
