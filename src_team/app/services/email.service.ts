import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';

import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Observable, of, Subject, Subscription } from 'rxjs';
import { map, switchMap, take, tap } from 'rxjs/operators';

import { environment } from '../../environments/environment';
import { MailCommand, QueueStatus, ServerProtocol } from '../common/enums/email-integration.enum';
import { MailType } from '../common/enums/mail.enum';
import { ApiResponse } from '../common/interfaces/api/api-response';
import { MailServer } from '../common/interfaces/api/responses/mail-server.interface';
import { DataResponseInterface } from '../common/interfaces/data-response.interface';
import { EmailMessageInterface, MailPriorityResponse } from '../common/interfaces/email-message-interface';
import { MailGmailAuthCodeData, MailGmailAuthResponse, MailOutlookAuthResponse } from '../common/interfaces/mail-o-auth2.interface';
import { MessageData } from '../common/interfaces/message-data.interface';
import { isSubscriptionUndefinedOrClosed } from '../common/utils/subscriptions-utils';
import { ConfirmDialogComponent } from '../shared/confirm-dialog/confirm-dialog.component';
import { selectMailType } from '../store/router/router.selectors';
import { SocketMailService } from './sockets/socket-mail.service';

@Injectable({
    providedIn: 'root'
})

export class EmailService {
    public endpointOutlookAuth: string = environment.apiUrl + 'mail_outlook_auth';
    public endpointGoogleAuth: string = environment.apiUrl + 'mail_gmail_auth';
    public allEmailsUrl: string = environment.apiUrl + 'mail_messages_received_all';
    public allSentEmailsUrl: string = environment.apiUrl + 'mail_messages_sent_all';
    public importantEmailsUrl: string = environment.apiUrl + 'mail_messages_received_important';
    public otherEmailsUrl: string = environment.apiUrl + 'mail_messages_received_other';
    public sendEmailUrl: string = environment.apiUrl + 'mail_send';
    public mailServerUrl: string = environment.apiUrl + 'mail_server';
    public receivedEmailUrl: string = environment.apiUrl + 'mail_received_mysql_message';
    public sentEmailsUrl: string = environment.apiUrl + 'mail_messages_sent_all';
    public refreshEmailsUrl: string = environment.apiUrl + 'mail_command';
    public draftUrl: string = environment.apiUrl + 'mail_sent_mysql_message';
    public mailFooterUrl: string = environment.apiUrl + 'mail_footer';

    private messageData: BehaviorSubject<MessageData> = new BehaviorSubject<MessageData>({message: '', senders: [], title: '', bodyFooter: ''});
    private openForwardEditorSubject: Subject<boolean> = new Subject<boolean>();
    private forwardMessageSubject: Subject<EmailMessageInterface> = new Subject<EmailMessageInterface>();
    private refreshTokenDialog$: Subscription;

    public openForwardEditor$: Observable<boolean> = this.openForwardEditorSubject.asObservable();
    public forwardMessage$: Observable<EmailMessageInterface> = this.forwardMessageSubject.asObservable();

    constructor(
        private readonly http: HttpClient,
        private readonly dialog: MatDialog,
        private readonly translateService: TranslateService,
        private readonly socketMailService: SocketMailService,
        private readonly store: Store
    ) {
    }

    getOutlookAuthUrl(): Observable<string> {
        return this.http.get<MailOutlookAuthResponse>(this.endpointOutlookAuth)
            .pipe(map((response: MailOutlookAuthResponse) => {
                return response?.results?.MailOutlookAuth?.auth_url;
            }));
    }

    getGmailAuthUrl(): Observable<string> {
        return this.http.get<MailGmailAuthResponse>(this.endpointGoogleAuth)
            .pipe(map((response: MailGmailAuthResponse) => {
                return response?.results?.MailGmailAuth?.auth_url;
            }));
    }

    setGmailAuthCredentialsAndGetEmail(data: MailGmailAuthCodeData): Observable<object> {
        return this.http.post(`${this.endpointGoogleAuth}`, JSON.stringify(data));
    }

    public updateGmailAuthCredentialsAndGetEmail(mailServerId: number, data: MailGmailAuthCodeData): Observable<object> {
        data = {
            MailGmailAuth: {
                ...data.MailGmailAuth,
                mail_server_id: mailServerId
            }
        };

        return this.http.post(`${this.endpointGoogleAuth}`, JSON.stringify(data));
    }

    getAllEmails(conditions: HttpParams): Observable<DataResponseInterface> {
        return this.http.get<DataResponseInterface>(this.allEmailsUrl, {
            params: conditions
        });
    }

    getEmailById(id: number) {
        return this.http.get<DataResponseInterface>(`${this.allEmailsUrl}?id=${id}`).pipe(
            map((response: DataResponseInterface) => response.results[0].MailMessagesReceivedAll)
        );
    }

    getEmailWithPriority(priority: string): Observable<unknown> {
        return this.http.get<DataResponseInterface>(`${this.allEmailsUrl}?priority=${priority}`).pipe(
            map((response: DataResponseInterface) => response.results[0].MailMessagesReceivedAll)
        );
    }

    getSentEmailById(id: string): Observable<unknown> {
        return this.http.get<DataResponseInterface>(`${this.allSentEmailsUrl}?id=${id}`).pipe(
            map(response => response.results[0].MailMessagesSentAll)
        );
    }

    getImportantEmails(conditions?: HttpParams, filters?: string) {
        return this.http.get<DataResponseInterface>(this.importantEmailsUrl + '?' + filters, {
            params: conditions
        });
    }

    getOtherEmails(conditions: HttpParams, filters?: string) {
        return this.http.get<DataResponseInterface>(this.otherEmailsUrl + '?' + filters, {
            params: conditions
        });
    }

    public getMailServer(protocolType?: ServerProtocol): Observable<ApiResponse<MailServer>> {
        let params: HttpParams = new HttpParams();

        if (protocolType) {
            params = params.set('server_protocol', protocolType.toString());
        }

        return this.http.get<ApiResponse<MailServer>>(this.mailServerUrl, { params }).pipe(
            switchMap((res: ApiResponse<MailServer>): Observable<ApiResponse<MailServer>> => {
                if (res?.results?.length <= 0) {
                    return of(res);
                }

                const mailServer: MailServer = res.results[0].MailServer;
                const serverProtocol: ServerProtocol = +mailServer.server_protocol as ServerProtocol;
                const isRefreshTokenValid: boolean = !!+mailServer.is_refresh_token_valid;

                // Jeżeli SMTP lub IMAP, to zwróć odpowiedź.
                if (serverProtocol === ServerProtocol.SMTP || serverProtocol === ServerProtocol.IMAP) {
                    return of(res);
                }

                // Jeżeli token jest nieważny, to wyświetl okno dialogowe o odświeżenie tokena.
                if (!isRefreshTokenValid) {
                    if (isSubscriptionUndefinedOrClosed(this.refreshTokenDialog$)) {
                        this.refreshTokenDialog$ = this.showRefreshTokenDialog(serverProtocol).subscribe();
                    }
                }

                return of(res);
            }
        ));
    }

    /**
     * Wyświetl okno dialogowe potwierdzające odświeżenie tokena.
     * @param serverProtocol Protokół serwera pocztowego.
     * @returns
     */
    private showRefreshTokenDialog(serverProtocol: ServerProtocol): Observable<boolean> {
        return this.dialog.open(
            ConfirmDialogComponent,
            {
                width: '400px',
                data: {
                    header: this.translateService.instant('MAIL-INTEGRATION.MODAL-REFRESH-INTEGRATION.TITLE'),
                    description: this.translateService.instant('MAIL-INTEGRATION.MODAL-REFRESH-INTEGRATION.DESCRIPTION'),
                    confirmText: this.translateService.instant('MAIL-INTEGRATION.MODAL-REFRESH-INTEGRATION.BUTTON.REFRESH-INTEGRATION')
                },
                panelClass: 'full-width-dialog'
            }
        ).afterClosed().pipe(
            take(1),
            tap((isConfirmed: boolean) => {
                if (isConfirmed) {
                    this.handleOAuthRefresh(serverProtocol);
                }
            })
        );
    }

    private getOAuthRedirectUrl(serverProtocol: ServerProtocol): Observable<string | null> {
        if (serverProtocol === ServerProtocol.GMAIL) {
            return this.getGmailAuthUrl();
        }

        if (serverProtocol === ServerProtocol.OUTLOOK) {
            return this.getOutlookAuthUrl();
        }

        return of(null);
    }

    private handleOAuthRefresh(serverProtocol: ServerProtocol): void {
        this.getOAuthRedirectUrl(serverProtocol).pipe(
            take(1),
        ).subscribe({
            next: (redirectUrl: string) => {
                if (redirectUrl) {
                    window.location.href = redirectUrl;
                }
            }
        });
    }

    sendMail(mailData): Observable<object> {
        return this.http.post(`${this.sendEmailUrl}`, mailData);
    }

    createDraftMail(mailData): Observable<object> {
        return this.http.post(`${this.draftUrl}`, mailData);
    }

    updateDraftMail(mailData, id): Observable<object> {
        return this.http.put(`${this.draftUrl}/${id}`, mailData);
    }

    getDrafts(conditions: HttpParams, filters?: string): Observable<DataResponseInterface> {
        conditions = conditions.append('status', QueueStatus.DRAFT.toString());

        return this.http.get<DataResponseInterface>(`${this.draftUrl}` + '?' + filters, {
            params: conditions
        });
    }

    deleteDraft(id: number): Observable<any> {
        const url = `${this.draftUrl}/${id}`;
        return this.http.delete<any>(url);
    }

    getSentEmails(conditions?: HttpParams, filters?: string) {
        conditions = conditions.append('status', QueueStatus.DONE.toString());
        return this.http.get<DataResponseInterface>(this.sentEmailsUrl + '?' + filters, {
            params: conditions
        });
    }

    refreshEmails(mailServerId: number) {
        const data = {
            'MailCommand': {
                'command_id': MailCommand.FETCH_QUEUE,
                'mail_server_id': mailServerId
            }
        };

        return this.http.post<DataResponseInterface>(this.refreshEmailsUrl, data);
    }

    setMessageData(data: MessageData | null): void {
        this.messageData.next(data || { senders: [], title: '', message: '' , bodyFooter: ''});
    }

    getMessageData(): Observable<MessageData> {
        return this.messageData.asObservable();
    }

    clearAllData(): void {
        this.messageData.next({ message: '', senders: [], title: '' , bodyFooter: ''});
    }

    getFooter() {
        return this.http.get(`${this.mailFooterUrl}`).pipe(
        map((result: DataResponseInterface) => result?.results['0']?.MailFooter)
        );
    }


    setForwardEditorState(state: boolean): void {
        this.openForwardEditorSubject.next(state);
    }

    setForwardMessage(message: EmailMessageInterface): void {
        this.forwardMessageSubject.next(message);
    }

    setPriority(id: number | string, mailType?: MailType): Observable<MailPriorityResponse> {
        if (mailType) {
            return this.setPriorityWithType(id, mailType);
        }

        return this.determineMailTypeFromUrl().pipe(
            switchMap(determinedType => this.setPriorityWithType(id, determinedType))
        );
    }

    private setPriorityWithType(id: number | string, mailType: MailType): Observable<MailPriorityResponse> {
        const url = mailType === MailType.RECEIVED ? this.receivedEmailUrl : this.draftUrl;
        const payload = mailType === MailType.RECEIVED
            ? {'MailReceivedMysqlMessage': {priority: 1}}
            : {'MailSentMysqlMessage': {priority: 1}};

        return this.http.put(url + '/' + id, payload);
    }

    unsetPriority(id: number | string, mailType?: MailType): Observable<MailPriorityResponse> {
        if (mailType) {
            return this.unsetPriorityWithType(id, mailType);
        }

        return this.determineMailTypeFromUrl().pipe(
            switchMap(determinedType => this.unsetPriorityWithType(id, determinedType))
        );
    }

    private unsetPriorityWithType(id: number | string, mailType: MailType): Observable<MailPriorityResponse> {
        const url = mailType === MailType.RECEIVED ? this.receivedEmailUrl : this.draftUrl;
        const payload = mailType === MailType.RECEIVED
            ? {'MailReceivedMysqlMessage': {priority: 0}}
            : {'MailSentMysqlMessage': {priority: 0}};

        return this.http.put(url + '/' + id, payload);
    }

    private determineMailTypeFromUrl(): Observable<MailType> {
        return this.store.select(selectMailType).pipe(
            take(1),
            map((mailTypeString: string) => {
                return mailTypeString === 'sent' ? MailType.SENT : MailType.RECEIVED;
            })
        );
    }

    remove(id): Observable<unknown> {
        return this.http.put(this.receivedEmailUrl + '/' + id, {'MailReceivedMysqlMessage': {is_remove: 1}});
    }

    removeDraft(id: number): Observable<unknown> {
        return this.http.delete(this.draftUrl + '/' + id).pipe(
            tap(() => {
                this.socketMailService.emitSentMailChange();
            })
        );
    }

    setIsRead(id: number): Observable<unknown> {
        return this.http.put(this.receivedEmailUrl + '/' + id, {'MailReceivedMysqlMessage': {is_read: 1}});
    }

    setIsUnread(id: number): Observable<unknown> {
        return this.http.put(this.receivedEmailUrl + '/' + id, {'MailReceivedMysqlMessage': {is_read: 0}});
    }
}
