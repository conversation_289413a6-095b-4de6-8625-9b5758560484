import {Component, Input, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {Subscription} from 'rxjs';
import {ClientUserStoreService} from '../../services/store/client-user-store.service';
import {AvatarStoreService} from '../../../../src_team/app/services/store/avatar-store.service';
import {ClientUserService} from '../../services/api/client-user.service';
import { AvatarModule } from 'ngx-avatars';

@Component({
    selector: 'app-employee-avatar-display',
    template: `
        <ngx-avatars
                data-cy=avatar
                [size]="size"
                [name]="name"
                [src]="actualAvatar ? actualAvatar : avatarStoreService.defaultAvatar"
        >
        </ngx-avatars>
    `,
    standalone: true,
    imports: [AvatarModule]
})
export class EmployeeAvatarDisplayComponent implements OnInit, OnDestroy {
    @Input() userId: number;
    @Input() size = 40;
    @Input() userAvatar: boolean;

    name: string;
    actualAvatar: string;

    subscription: Subscription;
    userSubscription: Subscription;

    constructor(
        private clientUserStoreService: ClientUserStoreService,
        private clientUserService: ClientUserService,
        public avatarStoreService: AvatarStoreService) {}

    ngOnInit() {

        this.subscription = this.clientUserStoreService.getEmployee(+this.userId).subscribe(user => {
            if (!user) {
                return;
            }

            const lastName = this.name = user.firstname ? ' ' + user.lastname : '';
            this.name = user.firstname + lastName;
            this.actualAvatar = user.avatar || ' ';
        });

        if (this.userAvatar) {
            this.getUserName();
        }
    }

    ngOnDestroy() {
        if (this.subscription) {
            this.subscription?.unsubscribe();
        }
        if (this.userSubscription) {
            this.userSubscription?.unsubscribe();
        }
    }

    getUserName() {
        this.userSubscription = this.clientUserService.getEmployee(+this.userId).subscribe(user => {
            if (!user) {
                return;
            }

            const lastName = this.name = user.User.firstname ? ' ' + user.User.lastname : '';
            this.name = user.User.firstname + lastName;
            this.actualAvatar = user.avatar || ' ';
        });
    }
}
