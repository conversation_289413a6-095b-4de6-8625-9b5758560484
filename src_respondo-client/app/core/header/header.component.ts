import {Component, Input, OnInit} from '@angular/core';
import {environment} from '../../../environments/environment';
import {ClientSocketsService} from '../../services/sockets/client-sockets.service';
import {SidebarsContainerService} from '../../../../src_team/app/services/sidebars-container.service';
import {IsMobileService} from '../../../../src_team/app/services/is-mobile.service';
import {AuthService} from '../../services/auth/auth.service';
import { MatToolbar } from '@angular/material/toolbar';
import { NgIf, AsyncPipe } from '@angular/common';
import { MatIconButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { DefaultLayoutDirective, DefaultLayoutAlignDirective } from 'ngx-flexible-layout/flex';
import { MatTooltip } from '@angular/material/tooltip';
import { ReloginComponent } from '../relogin/relogin.component';
import { EmployeeAvatarDisplayComponent } from '../../client-shared/employee-avatar-display/employee-avatar-display.component';
import { MatMenuTrigger, MatMenu } from '@angular/material/menu';
import { SsoComponent } from '../sso/sso.component';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
    selector: 'app-header',
    templateUrl: './header.component.html',
    styleUrls: ['./header.component.scss'],
    standalone: true,
    imports: [MatToolbar, NgIf, MatIconButton, MatIcon, DefaultLayoutDirective, DefaultLayoutAlignDirective, MatTooltip, ReloginComponent, EmployeeAvatarDisplayComponent, MatMenuTrigger, MatMenu, SsoComponent, AsyncPipe, TranslatePipe]
})
export class HeaderComponent implements OnInit {
    actualUserId = null;
    applicationStage = '';
    environment = environment;

    @Input() expertAccount: number | null;

    constructor(
        private sidebarsContainer: SidebarsContainerService,
        public socketsService: ClientSocketsService,
        public isMobileService: IsMobileService,
        private authService: AuthService
    ) {
    }

    ngOnInit() {
        if (environment.apiUrl.includes('.beta.')) {
            this.applicationStage = 'Beta';
        } else if (environment.apiUrl.includes('.rc.')) {
            this.applicationStage = 'RC';
        }

        this.actualUserId = this.authService.getUserId();
    }

    menuSidebarToggle() {
        this.sidebarsContainer.sidebar('menu').toggle();
    }
}
