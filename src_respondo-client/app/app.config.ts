import {ApplicationConfig, importProvidersFrom} from '@angular/core';
import {BrowserModule} from '@angular/platform-browser';
import {AppRoutes, routes} from './app.routes';
import {AgGridModule} from 'ag-grid-angular';
import {ServiceWorkerModule} from '@angular/service-worker';
import {environment} from '../environments/environment';
import {TranslateLoader, TranslateModule} from '@ngx-translate/core';
import {HTTP_INTERCEPTORS, HttpClient, provideHttpClient, withInterceptorsFromDi} from '@angular/common/http';
import {APP_NAME} from '../../src_team/app/common/tokens/app-name-token';
import {ClientAuthInterceptor} from './services/auth/client-auth-interceptor.service';
import {httpTranslateLoaderFactory} from '../main';
import {provideRouter} from '@angular/router';
import {icons, LucideAngularModule} from 'lucide-angular';

export const appConfig: ApplicationConfig = {
    providers: [
        provideRouter(routes),
        importProvidersFrom(BrowserModule, AgGridModule, LucideAngularModule.pick(icons), ServiceWorkerModule.register('/ngsw-worker.js', { enabled: environment.production }), TranslateModule.forRoot({
            loader: {
                provide: TranslateLoader,
                useFactory: httpTranslateLoaderFactory,
                deps: [HttpClient]
            }
        })),
        {
            provide: APP_NAME,
            useValue: 'PANEL_KLIENTA'
        },
        {
            provide: HTTP_INTERCEPTORS,
            useClass: ClientAuthInterceptor,
            multi: true
        },
        provideHttpClient(withInterceptorsFromDi())
    ]
}
