import {Injectable} from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import {EMPTY, Observable, of} from 'rxjs';
import {catchError, map, pluck, switchMap} from 'rxjs/operators';
import {environment} from '../../../environments/environment';
import {TranslateService} from '@ngx-translate/core';
import {UserAvatarService} from '../../../../src_team/app/services/user-avatar.service';
import {InterceptorSkipHeader} from '../auth/client-auth-interceptor.service';
import {AuthService} from '../auth/auth.service';

@Injectable({
    providedIn: 'root'
})
export class ClientUserService {
    constructor(private http: HttpClient, private authService: AuthService, private avatarService: UserAvatarService, private translate: TranslateService) {
    }

    confirmRemoveUser(userId: string, hash: string): Observable<any> {
        const formData = new FormData();

        formData.append('data[User][hash]', hash);
        formData.append('data[User][user_id]', userId);

        return this.http.post(environment.apiUrlRoot + '/users/confirmRemove', formData, {headers: new HttpHeaders().set(InterceptorSkipHeader, '')});
    }

    deleteUser(): Observable<any> {
        return this.http.delete(environment.apiUrl + 'user/' +  this.authService.getUserId());
    }

    getEmployee(id: number) {
        return this.http.get<{UserEmployee: any}>(environment.apiUrl + 'user_employee/' + id).pipe(
            pluck('UserEmployee'),
            map(employee => ({...employee, is_public: 1})),
            map(employee => ({...employee, User: employee})),
            switchMap(employee => this.avatarService.addUserAvatar(employee)),
            catchError(() => of({id: 0, firstname: this.translate.instant('SERVICES.EMPLOYEE'), lastname: '', is_public: 0}))
        );
    }

    getUser(userId: number): Observable<any> {
        if (!+userId) {
            return EMPTY;
        }

        return this.http.get(environment.apiUrl + 'user/' + userId);
    }

    getUserHistoryChanges(): Observable<any> {
        const params = new HttpParams()
            .set('model_name', 'User,Contractor')
            .set('limit', '9999');

        return this.http.get(environment.apiUrl + 'modification_history_change', { params }).pipe(
            map((response: any) => {
                return {
                    ...response
                };
            })
        );
    }

    changeEmail(email, emailConfirmation, password): Observable<any> {
        return this.http.put(environment.apiUrl + 'user_change_email/' + this.authService.getUserId(),
            {User: {email, email_confirmation: emailConfirmation, password}}
        );
    }

    changePassword(newPassword, confirmPassword, oldPassword): Observable<any> {
        return this.http.put(environment.apiUrl + 'user_change_password/' + this.authService.getUserId(),
            {User: {password: newPassword, password_confirmation: confirmPassword, password_old: oldPassword}}
        );
    }

    changeLanguage(language) {
        this.translate.use(language);

        return this.http.put(environment.apiUrl + 'user/' + this.authService.getUserId(), JSON.stringify({User: {language}}));
    }
}
