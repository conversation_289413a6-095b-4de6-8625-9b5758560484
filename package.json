{"engines": {"node": "22.0.0"}, "name": "respondo-ui", "version": "20.5.0", "scripts": {"ng": "ng", "start": "ng serve", "start-team": "ng serve --project=respondo-team", "start-local-client": "set NODE_OPTIONS=--openssl-legacy-provider && ng serve --project=respondo-client", "build": "ng build", "build:prod": "ng build --configuration production", "build:team": "ng build respondo-team --configuration production", "build:client": "ng build respondo-client --configuration production", "test": "ng test", "ut": "ng test respondo-team --watch=false", "lint": "ng lint", "e2e": "ng e2e", "dist": "http-server -p 4200 -c-1 dist/", "dist:client": "http-server -p 8080 -c-1 dist/respondo-client", "start:team": "APP=respondo-team vite"}, "lint-staged": {"*.ts": "tslint"}, "private": true, "dependencies": {"@angular-slider/ngx-slider": "19.0.0", "@angular/animations": "19.2.7", "@angular/cdk": "19.2.10", "@angular/common": "19.2.7", "@angular/compiler": "19.2.7", "@angular/core": "19.2.7", "@angular/forms": "19.2.7", "@angular/localize": "^19.2.7", "@angular/material": "19.2.10", "@angular/platform-browser": "19.2.7", "@angular/platform-browser-dynamic": "19.2.7", "@angular/router": "19.2.7", "@angular/service-worker": "19.2.7", "@covalent/core": "^10.1.0", "@fullcalendar/angular": "6.1.17", "@fullcalendar/core": "6.1.17", "@fullcalendar/daygrid": "6.1.17", "@fullcalendar/interaction": "6.1.17", "@maskito/angular": "^3.7.1", "@maskito/core": "^3.7.1", "@ngrx/effects": "^19.1.0", "@ngrx/router-store": "^19.1.0", "@ngrx/store": "^19.1.0", "@ngrx/store-devtools": "^19.1.0", "@ngx-translate/core": "16.0.4", "@ngx-translate/http-loader": "6.0.0", "@swimlane/ngx-charts": "^20.5.0", "@types/socket.io-client": "1.4.32", "@zxcvbn-ts/core": "^3.0.4", "@zxcvbn-ts/language-en": "^3.0.0", "ag-grid-angular": "^33.2.4", "ag-grid-community": "^33.2.4", "dayjs": "^2.0.0-alpha.4", "dragula": "^3.7.3", "file-saver": "^2.0.5", "html-to-pdfmake": "1.3.6", "lucide-angular": "^0.503.0", "ngx-avatars": "1.8.0", "ngx-clipboard": "16.0.0", "ngx-color": "9.0.0", "ngx-cookie-service": "19.1.2", "ngx-flexible-layout": "19.0.0", "ngx-mat-select-search": "^8.0.0", "ngx-order-pipe": "3.0.0", "ngx-quill": "20.0.0", "ngx-ui-tour-core": "14.0.0", "ngx-ui-tour-md-menu": "^14.0.1", "ngx-uploader": "17.0.1", "pdfmake": "0.1.64", "pdfmake-wrapper": "2.0.0", "quill": "1.3.7", "rxjs": "7.5.7", "socket.io-client": "2.5.0", "underscore": "^1.13.6", "uuid": "^9.0.1", "xlsx": "^0.18.5", "zone.js": "~0.15.0", "zxcvbn3": "^0.1.1"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.8", "@angular-devkit/schematics": "^19.2.8", "@angular/cli": "^19.2.8", "@angular/compiler-cli": "19.2.7", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "^2.0.13", "@types/node": "~16.7.12", "angular-eslint": "19.3.0", "codelyzer": "^6.0.2", "eslint": "^9.23.0", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-coverage-istanbul-reporter": "^3.0.3", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "karma-spec-reporter": "^0.0.36", "lint-staged": "^15.5.1", "ts-node": "~8.4.1", "tslint": "~6.1.0", "typescript": "~5.8.3", "typescript-eslint": "8.27.0"}}